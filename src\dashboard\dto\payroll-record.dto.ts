import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';

export class PayrollRecordDto {
  @ApiProperty()
  @IsString()
  @MaxLength(255)
  firstName: string;

  @ApiProperty()
  @IsString()
  @MaxLength(255)
  lastName: string;

  @ApiProperty()
  @IsString()
  staffCode: string;

  @ApiProperty()
  @IsNumber()
  basicSalary: number;

  @ApiProperty()
  @IsNumber()
  allowances: number;

  @ApiProperty()
  @IsNumber()
  deductions: number;

  @ApiProperty()
  @IsNumber()
  tax: number;

  @ApiProperty()
  @IsNumber()
  netPay: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  bankName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  accountNumber?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  employeeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  payrollId?: string;
}
