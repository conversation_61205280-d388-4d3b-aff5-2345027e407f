import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { AuthorizationModule } from 'src/authorization-queue/authorization-queue.module';
import { DatabaseModule } from 'src/database/database.module';
import { RegionService } from 'src/region/region.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { UnitController } from './unit.controller';
import { UnitService } from './unit.service';

@Module({
  imports: [DatabaseModule, AuthorizationModule],
  providers: [
    UnitService,
    AuthTokenService,
    RegionService,
    TaxJurisdictionService,
  ],
  controllers: [UnitController],
})
export class UnitModule {}
