import { Injectable } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';

@Injectable()
export class TaxService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  async getTaxReports({
    branchName,
    token,
    payrollId,
  }: {
    token: string;
    branchName: string;
    payrollId: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;

      console.log(companyId);

      // const taxReports = await this.databaseService.payrollRecord.findMany({
      //   where: {
      //     employee: {
      //       companyId,
      //       branchName,
      //     },
      //     payrollId,
      //   },
      //   include: {
      //     employee: {
      //       select: {
      //         taxId: true,
      //         firstName: true,
      //         lastName: true,
      //       },
      //     },
      //   },
      // });

      return 'Pending';
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
