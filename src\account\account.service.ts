import { BadRequestException, Injectable } from '@nestjs/common';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseService } from 'src/database/database.service';
import { CreateAccountDto } from './dto/create-account.dto';

@Injectable()
export class AccountService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cryptoService: CryptoService,
  ) {}

  generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  async ensureUniqueSlug(baseSlug: string): Promise<string> {
    let slug = baseSlug;
    let suffix = 1;

    while (
      await this.databaseService.account.findUnique({
        where: { slug },
      })
    ) {
      slug = `${baseSlug}-${suffix++}`;
    }

    return slug;
  }

  async getAccountData(slug: string) {
    console.log(slug);

    const company = await this.databaseService.account.findUnique({
      where: { slug },
      include: {
        companies: {
          select: {
            id: true,
            name: true,
            status: true,
            logo: true,
            registrationNumber: true,
          },
        },
      },
    });

    console.log(company);

    return company;
  }

  async findAccountByEmail(email: string) {
    return this.databaseService.account.findUnique({
      where: { email },
    });
  }

  async createAccount(payload: CreateAccountDto) {
    const { email, password, name } = payload;

    const existingCompany = await this.findAccountByEmail(email);
    if (existingCompany) {
      throw new BadRequestException('Company with email already exists');
    }

    const baseSlug = this.generateSlug(name);
    const uniqueSlug = await this.ensureUniqueSlug(baseSlug);
    const passwordHash = await this.cryptoService.hash(password);

    // const count = await this.databaseService.company.count();

    const newCompany = await this.databaseService.account.create({
      data: {
        name,
        email,
        password: passwordHash,
        // id: ********** + count,
        roleId: 100,
        slug: uniqueSlug,
      },
    });

    return newCompany;
  }
}
