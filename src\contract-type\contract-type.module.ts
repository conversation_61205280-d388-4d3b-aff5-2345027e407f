import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { ContractTypeController } from './contract-type.controller';
import { ContractTypeService } from './contract-type.service';

@Module({
  imports: [DatabaseModule],
  providers: [ContractTypeService, AuthTokenService],
  controllers: [ContractTypeController],
})
export class ContractTypeModule {}
