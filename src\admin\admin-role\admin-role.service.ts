import { ConflictException, Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { AdminRoleByIdDto } from './dto/admin-role-by-id.dto';
import { CreateAdminRoleDto } from './dto/create-admin-role.dto';

@Injectable()
export class AdminRoleService {
  constructor(private readonly databaseService: DatabaseService) {}

  async findRoleByRoleId(payload: AdminRoleByIdDto) {
    const { roleId } = payload;

    try {
      const role = await this.databaseService.adminRole.findUnique({
        where: {
          id: roleId,
        },
      });

      return role;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async createRole(payload: CreateAdminRoleDto) {
    const { name, description } = payload;

    try {
      // Check if role with the same name already exists
      const existingRole = await this.databaseService.adminRole.findUnique({
        where: { name },
      });

      if (existingRole) {
        throw new ConflictException('Role name already exists');
      }

      const role = await this.databaseService.adminRole.create({
        data: {
          name,
          description,
        },
      });

      return role;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async findAllRoles() {
    try {
      return await this.databaseService.adminRole.findMany();
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
}
