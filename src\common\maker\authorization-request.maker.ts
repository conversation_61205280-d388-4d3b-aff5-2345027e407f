// src/common/guards/privilege.guard.ts
import { ConflictException, Injectable } from '@nestjs/common';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { DatabaseService } from 'src/database/database.service';

@Injectable()
export class AuthorizationRequestMaker {
  constructor(private databaseService: DatabaseService) {}

  async queueRequest({
    payload,
    companyId,
    action,
    module,
    requestedBy,
  }: {
    payload: any;
    companyId: string;
    module: MODULE_CONSTANT;
    action: ACTIONS_CONSTANT;
    requestedBy: string;
  }) {
    try {
      // Check for pending requests with the same name for this module and company
      // Only validate for CREATE, UPDATE, and DELETE actions (not bulk operations or view)
      if (
        [
          ACTIONS_CONSTANT.create,
          ACTIONS_CONSTANT.update,
          ACTIONS_CONSTANT.delete,
        ].includes(action)
      ) {
        await this.validateNoPendingRequest(payload, companyId, module, action);
      }

      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload),
          action,
          module,
          requestedBy,
          company: {
            connect: {
              id: companyId,
            },
          },
        },
      });
      return;
    } catch (error) {
      console.log(error);

      // Re-throw ConflictException to preserve the specific error
      if (error instanceof ConflictException) {
        throw error;
      }

      throw new Error('Something went wrong. Try again!');
    }
  }

  private async validateNoPendingRequest(
    payload: any,
    companyId: string,
    module: MODULE_CONSTANT,
    action: ACTIONS_CONSTANT,
  ) {
    // Extract the name from the payload
    const name = payload?.name;

    if (!name) {
      // If there's no name field, we can't validate - let it proceed
      return;
    }

    // Check for existing pending requests for this name, module, and company
    const existingPendingRequest =
      await this.databaseService.authorizationQueue.findFirst({
        where: {
          companyId,
          module,
          status: 'PENDING',
          data: {
            contains: `"name":"${name}"`,
          },
        },
      });

    if (existingPendingRequest) {
      const actionText =
        action === ACTIONS_CONSTANT.create
          ? 'creation'
          : action === ACTIONS_CONSTANT.update
            ? 'update'
            : 'deletion';

      throw new ConflictException(
        `A ${actionText} request for "${name}" in ${module.toLowerCase()} is already pending authorization. Please wait for approval or rejection before submitting a new request.`,
      );
    }
  }
}
