// dto/contact-info.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString } from 'class-validator';

export class ContactInfoDto {
  @ApiProperty({ example: '08012345678' })
  @IsString()
  phone1: string;

  @ApiProperty({ example: '08123456789', required: false })
  @IsOptional()
  @IsString()
  phone2?: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;
}
