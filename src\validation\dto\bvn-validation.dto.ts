import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsInt, IsString } from 'class-validator';

export class BvnValidationDto {
  @ApiProperty({
    example: 95888168924,
  })
  @Allow()
  @IsInt({ message: 'Kindly provide a valid 11 digit number' })
  bvn: string;

  @ApiProperty({
    example: 'Bunch',
  })
  @Allow()
  @IsString({ message: 'Kindly provide a valid first name' })
  firstname: string;

  @ApiProperty({
    example: 'Dillon',
  })
  @Allow()
  @IsString({ message: 'Kindly provide a valid first name' })
  lastname: string;
}
