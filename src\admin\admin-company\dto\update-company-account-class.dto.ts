import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class UpdateCompanyAccountClassDto {
  @ApiProperty({ description: 'The account class', example: 'Premium' })
  @IsString()
  @IsNotEmpty({ message: 'Account Class is required' })
  accountClass: string;

  @ApiProperty({ description: 'The company ID', example: 'company_123' })
  @IsString()
  @IsNotEmpty({ message: 'Company Id is required' })
  companyId: string;
}
