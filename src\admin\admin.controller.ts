import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  AdminRateLimit,
  AuthRateLimit,
  ReadRateLimit,
  WriteRateLimit,
} from 'src/common/decorators/rate-limit.decorator';
import { Public } from 'src/common/decorators/public.decorator';
import { AdminAccountService } from './admin-account/admin-account.service';
import { GetAccountByIdDto } from './admin-account/dto/get-account-by-id.dto';
import { AdminAuthService } from './admin-auth/auth.service';
import { AdminAuthDto } from './admin-auth/dto/admin-auth.dto';
import { AdminCompanyService } from './admin-company/admin-company.service';
import { UpdateCompanyAccountClassDto } from './admin-company/dto/update-company-account-class.dto';
import { UpdateCompanyAccountPrefixDto } from './admin-company/dto/update-company-account-prefix.dto';
import { AdminPayrollService } from './admin-payroll/admin-company.service';
import { AdminRoleService } from './admin-role/admin-role.service';
import { CreateAdminRoleDto } from './admin-role/dto/create-admin-role.dto';
import { AdminUserService } from './admin-user/admin-user.service';
import { CreateAdminUserDto } from './admin-user/dto/create-admin-user.dto';

@ApiTags('Admin')
@Controller('admin')
export class AdminController {
  constructor(
    private readonly adminAuthService: AdminAuthService,
    private readonly adminUserService: AdminUserService,
    private readonly adminRoleService: AdminRoleService,
    private readonly adminCompanyService: AdminCompanyService,
    private readonly adminAccountService: AdminAccountService,
    private readonly adminPayrollService: AdminPayrollService,
  ) {}

  @AuthRateLimit()
  @Public()
  @Post('/auth/login')
  authenticateAdminUser(@Body() payload: AdminAuthDto) {
    return this.adminAuthService.signIn(payload);
  }

  @AdminRateLimit()
  @Post('/user/create')
  createAdminUser(@Body() payload: CreateAdminUserDto) {
    return this.adminUserService.createAdminuser(payload);
  }

  @AdminRateLimit()
  @Post('/role/create')
  createAdminRole(@Body() payload: CreateAdminRoleDto) {
    return this.adminRoleService.createRole(payload);
  }

  @Get('/role/read')
  roleAdminRole() {
    return this.adminRoleService.findAllRoles();
  }

  @Get('/company/read')
  getAllCompany() {
    return this.adminCompanyService.getAllCompany();
  }

  @Post('/company/account-class/update')
  updateCompanyAccountClass(@Body() payload: UpdateCompanyAccountClassDto) {
    return this.adminCompanyService.updateCompanyAccountClass(payload);
  }

  @Post('/company/account-prefix/update')
  updateCompanyAccountPrefix(@Body() payload: UpdateCompanyAccountPrefixDto) {
    return this.adminCompanyService.updateCompanyAccountPrefix(payload);
  }

  @Get('/account/read')
  getAllAccount() {
    return this.adminAccountService.getAllAccounts();
  }

  @Get('/account/read/:accountId')
  getAccountById(@Param('accountId', ParseIntPipe) accountId: number) {
    return this.adminAccountService.getAccountById({ accountId });
  }

  @Post('/account/delete')
  deleteAccount(@Body() payload: GetAccountByIdDto) {
    return this.adminAccountService.deleteAccount(payload);
  }

  @Get('/payroll/read')
  getAllPayroll() {
    return this.adminPayrollService.getAllPayroll();
  }
}
