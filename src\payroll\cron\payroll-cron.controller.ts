import { <PERSON>, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { PayrollCronService } from './payroll-cron.service';

@ApiTags('Payroll Cron')
@Controller('payroll-cron')
export class PayrollCronController {
  constructor(private readonly cronService: PayrollCronService) {}

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Trigger cron job manually to handle pending payrollbulk jobs',
    statusCodes: [],
  })
  @Public()
  @Post('test')
  async triggerCronManually() {
    return this.cronService.handlePendingBulkJobs();
  }
}
