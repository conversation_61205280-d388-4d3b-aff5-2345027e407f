import { BadRequestException, Injectable, Logger } from '@nestjs/common';

import { Cron, CronExpression } from '@nestjs/schedule';
import { Status } from '@prisma/client';
import { DatabaseService } from 'src/database/database.service';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { EmployeeService } from '../employee.service';

@Injectable()
export class EmployeeCronService {
  private readonly logger = new Logger(EmployeeCronService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly employeeService: EmployeeService,
  ) {}

  async handlePendingBulkJobs() {
    const records = await this.db.bulkEmployeeRecord.findMany({
      where: { status: Status.PENDING },
      take: 10,
      include: {
        job: {
          include: {
            company: {
              select: { id: true },
            },
          },
        },
      },
    });

    this.logger.log(`Processing ${records.length} pending employee records`);

    for (const record of records) {
      try {
        const { job } = record;

        const newEmp = await this.employeeService.createEmployeeAndBankAccount({
          data: JSON.parse(JSON.stringify(record.payload)) as CreateEmployeeDto,
          approvedBy: job.approvedBy!,
          requestedBy: job.createdBy,
          companyId: job.companyId,
        });

        if (newEmp) {
          await this.db.bulkEmployeeRecord.delete({
            where: { id: record.id },
          });

          await this.db.bulkEmployeeJob.update({
            where: {
              id: record.jobId,
            },
            data: {
              successCount: {
                increment: 1,
              },
            },
          });
        }
      } catch (error) {
        this.logger.error(`Failed to process record ${record.id}: ${error}`);

        let reason = 'Server error';

        console.log(error instanceof BadRequestException);
        if (error instanceof BadRequestException) {
          const res = error.getResponse();

          if (typeof res === 'string') {
            reason = res;
          } else if (
            typeof res === 'object' &&
            res !== null &&
            'message' in res
          ) {
            const message = (res as { message: string | string[] }).message;
            reason = Array.isArray(message) ? message.join(', ') : message;
          }
        }

        await this.db.bulkEmployeeRecord.update({
          where: { id: record.id },
          data: {
            status: Status.FAILED,
            failureReason: reason,
          },
        });

        await this.db.bulkEmployeeJob.update({
          where: {
            id: record.jobId,
          },
          data: {
            failureCount: {
              increment: 1,
            },
          },
        });
      }
    }
  }
  async finalizeCompletedJobs() {
    // 1. Find all jobs that are still PENDING
    const pendingJobs = await this.db.bulkEmployeeJob.findMany({
      where: { status: Status.PENDING },
      include: {
        records: true,
      },
    });

    for (const job of pendingJobs) {
      // 2. Check if job has any records with status PENDING
      const hasPendingRecords = job.records.some(
        (record) => record.status === Status.PENDING,
      );

      // 3. If NO pending records, update job status to COMPLETED
      if (!hasPendingRecords) {
        await this.db.bulkEmployeeJob.update({
          where: { id: job.id },
          data: {
            status: Status.COMPLETED,
          },
        });
        this.logger.log(`Marked job ${job.id} as COMPLETED`);
      }
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleCron() {
    await this.handlePendingBulkJobs();
    await this.finalizeCompletedJobs();
  }
}
