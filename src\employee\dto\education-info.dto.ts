// dto/education-info.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class EducationInfoDto {
  @ApiProperty({ example: 'BSc' })
  @IsString()
  highestQualification: string;

  @ApiProperty({ example: 'Computer Science' })
  @IsString()
  course: string;

  @ApiProperty({ example: 'University of Lagos', required: false })
  @IsOptional()
  @IsString()
  institutionName?: string;

  @ApiProperty({ example: '123 Street Yaba', required: false })
  @IsOptional()
  @IsString()
  institutionAddress?: string;

  @ApiProperty({ example: '2014-07-01', required: false })
  @IsOptional()
  @IsString()
  dateOfGraduation?: string;
}
