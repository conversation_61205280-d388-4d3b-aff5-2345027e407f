import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { GetAccountByIdDto } from './dto/get-account-by-id.dto';

@Injectable()
export class AdminAccountService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  async getAccountById(payload: GetAccountByIdDto) {
    // Todo: Confirm user is an admin
    return await this.databaseService.account.findUnique({
      where: {
        id: payload.accountId,
      },
    });
  }

  async getAllAccounts() {
    // Todo: Confirm user is an admin
    const accounts = await this.databaseService.account.findMany({
      include: {
        companies: {
          select: {
            _count: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return accounts.map((account) => {
      const { name, createdAt, slug, email, companies, id, status } = account;
      return {
        id,
        name,
        slug,
        email,
        numberOfCompanies: companies.length,
        createdAt,
        status,
      };
    });
  }

  async deleteAccount(payload: GetAccountByIdDto) {
    try {
      const accountExist = await this.getAccountById({
        accountId: payload.accountId,
      });

      if (!accountExist) {
        throw new BadRequestException('Account not found');
      }

      await this.databaseService.account.update({
        where: {
          id: accountExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });
      return;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // async updateCompanyAccountPrefix(payload: UpdateCompanyAccountPrefixDto) {
  //   try {
  //     const companyExist = await this.getCompanyById({
  //       companyId: payload.companyId,
  //     });

  //     if (!companyExist) {
  //       throw new BadRequestException('Company not found');
  //     }

  //     await this.databaseService.company.update({
  //       where: {
  //         id: companyExist.id,
  //       },
  //       data: {
  //         accountPrefix: payload.accountPrefix,
  //       },
  //     });
  //     return;
  //   } catch (error) {
  //     console.log(error);
  //     throw error;
  //   }
  // }
}
