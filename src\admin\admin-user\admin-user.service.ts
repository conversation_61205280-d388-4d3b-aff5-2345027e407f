import { BadRequestException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseService } from 'src/database/database.service';
import { OtpService } from 'src/otp/otp.service';
import { AdminRoleService } from '../admin-role/admin-role.service';
import { CreateAdminUserDto } from './dto/create-admin-user.dto';

@Injectable()
export class AdminUserService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cryptoService: CryptoService,
    private jwtService: JwtService,
    private readonly authTokenService: AuthTokenService,
    private readonly otpService: OtpService,
    private readonly adminRoleService: AdminRoleService,
  ) {}

  async createAdminuser(payload: CreateAdminUserDto) {
    const { email, password, name, roleId } = payload;

    try {
      const userExist = await this.databaseService.adminUser.findUnique({
        where: {
          email: email,
        },
      });

      if (userExist) {
        throw new BadRequestException(
          'An Admin user with the email provided exist',
        );
      }

      const role = await this.adminRoleService.findRoleByRoleId({ roleId });

      if (!role) {
        throw new BadRequestException('Role not found');
      }

      const passwordHash = await this.cryptoService.hash(password);

      await this.databaseService.adminUser.create({
        data: {
          name,
          email,
          password: passwordHash,
          roleId: role.id,
        },
      });

      // TODO: Send email to the user

      return;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
