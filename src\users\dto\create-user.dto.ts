import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsEmail,
  IsInt,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({
    description: 'The full name of the user.',
    example: '<PERSON>',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The email address of the user.',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'The password for the user account.',
    example: 'strongpassword123',
  })
  @IsString()
  password: string;

  @ApiProperty({
    description: 'The list of branch IDs the user belongs to.',
    example: ['1', '2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  branches: string[];

  @ApiProperty({
    description: 'The role ID assigned to the user.',
    example: 100,
  })
  @IsInt()
  roleId: number;

  @ApiProperty({
    description: 'Whether two-factor authentication is enabled for the user.',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @ApiProperty({
    description: 'Whether the user can access all branches.',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  hasAccessToAllBranches?: boolean;

  @ApiProperty({
    description:
      'Whether the user is a root user (created along with the company).',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isRoot?: boolean;
}
