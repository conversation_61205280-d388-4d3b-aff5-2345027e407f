import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { AuthorizationModule } from 'src/authorization-queue/authorization-queue.module';
import { BranchService } from 'src/branch/branch.service';
import { DatabaseModule } from 'src/database/database.module';
import { RegionService } from 'src/region/region.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { SubBranchController } from './sub-branch.controller';
import { SubBranchService } from './sub-branch.service';

@Module({
  imports: [DatabaseModule, AuthorizationModule],
  providers: [
    SubBranchService,
    AuthTokenService,
    RegionService,
    TaxJurisdictionService,
    BranchService,
  ],
  controllers: [SubBranchController],
})
export class SubBranchModule {}
