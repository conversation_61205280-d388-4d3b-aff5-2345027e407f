import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { RoleController } from './role.controller';
import { RoleService } from './role.service';

@Module({
  imports: [DatabaseModule],
  providers: [RoleService, AuthTokenService],
  controllers: [RoleController],
})
export class RoleModule {}
