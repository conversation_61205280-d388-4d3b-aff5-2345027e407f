import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { DatabaseService } from 'src/database/database.service';
import {
  DEFAULT_ENTITIES,
  DEFAULT_ENTITY_ACTIONS,
} from './constants/entity.constants';
import { CreateEntityDto } from './dto/create-entity.dto';

@Injectable()
export class EntityService {
  constructor(private readonly databaseService: DatabaseService) {}

  async getEntities() {
    try {
      return await this.databaseService.businessEntity.findMany({});
    } catch (error) {
      console.log(error);

      throw new Error('Failed to fetch entities');
    }
  }
  async createEntity(payload: CreateEntityDto) {
    const { name: entityName, description } = payload;
    try {
      const createdEntity = await this.databaseService.$transaction(
        async (tx) => {
          // Check if the entity already exists
          const newEntity = await tx.businessEntity.upsert({
            where: {
              name: entityName.toUpperCase(),
            },
            update: {
              name: entityName.toUpperCase(),
              description,
            },
            create: {
              name: entityName.toUpperCase(),
              description,
            },
          });

          const actionsData: Prisma.EntityActionCreateManyInput[] =
            DEFAULT_ENTITY_ACTIONS.map((action) => ({
              name: `${newEntity.name}_${action}`.toUpperCase(),
              entityId: newEntity.id,
            }));

          await tx.entityAction.createMany({
            data: actionsData,
            skipDuplicates: true,
          });

          // Step 3: Create Privileges for each action
          const createdActionRecords = await tx.entityAction.findMany({
            where: { entityId: newEntity.id },
          });

          const privilegesData: Prisma.PrivilegeCreateManyInput[] =
            createdActionRecords.map((action) => ({
              name: action.name,
              actionId: action.id,
            }));

          await tx.privilege.createMany({
            data: privilegesData,
            skipDuplicates: true,
          });

          // Fetch the created privileges to get their IDs
          const createdPrivileges = await tx.privilege.findMany({
            where: {
              actionId: createdActionRecords[0].id,
            },
          });

          console.log(createdPrivileges);

          // Map them into RolePrivilegeCreateManyInput format
          const rolePrivilegesData = createdPrivileges.map((privilege) => ({
            privilegeName: privilege.name,
          }));

          await tx.role.update({
            where: {
              id: 100,
            },
            data: {
              RolePrivilege: {
                createMany: {
                  data: rolePrivilegesData,
                  skipDuplicates: true,
                },
              },
            },
          });

          return {
            info: `Entity '${newEntity.name}' created with ${createdActionRecords.length} actions and privileges.`,
            createdActionRecords,
            createdPrivileges: privilegesData,
            entity: newEntity,
          };
        },
      );
      return createdEntity;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async seedDefaultEntities() {
    try {
      // const results: [] = [];

      const results: {
        entity: {
          id: string;
          name: string;
          description: string | null;
          createdAt: Date;
          updatedAt: Date;
        };
        [key: string]: unknown;
      }[] = [];

      for (const name of DEFAULT_ENTITIES) {
        const result = await this.databaseService.$transaction(async (tx) => {
          // 1. Upsert entity
          const newEntity = await tx.businessEntity.upsert({
            where: { name },
            update: { name },
            create: {
              name,
              description: `${name} entity`.replace(/_/g, ' ').toLowerCase(),
            },
          });

          // 2. Create actions for the entity
          const actionsData: Prisma.EntityActionCreateManyInput[] =
            DEFAULT_ENTITY_ACTIONS.map((action) => ({
              name: `${newEntity.name}|${action}`.toUpperCase(),
              entityId: newEntity.id,
            }));

          await tx.entityAction.createMany({
            data: actionsData,
            skipDuplicates: true,
          });

          const createdActions = await tx.entityAction.findMany({
            where: { entityId: newEntity.id },
          });

          // 3. Create privileges for each action
          const privilegesData: Prisma.PrivilegeCreateManyInput[] =
            createdActions.map((action) => ({
              name: action.name,
              actionId: action.id,
            }));

          await tx.privilege.createMany({
            data: privilegesData,
            skipDuplicates: true,
          });

          const createdPrivileges = await tx.privilege.findMany({
            where: {
              actionId: {
                in: createdActions.map((action) => action.id),
              },
            },
          });

          // 4. Assign privileges to super admin role (id: 100)
          const rolePrivilegesData = createdPrivileges.map((privilege) => ({
            privilegeName: privilege.name,
          }));

          await tx.role.update({
            where: { id: 100 },
            data: {
              RolePrivilege: {
                createMany: {
                  data: rolePrivilegesData,
                  skipDuplicates: true,
                },
              },
            },
          });

          return {
            entity: newEntity,
            actions: createdActions,
            privileges: createdPrivileges,
          };
        });

        results.push(result);
      }

      return {
        message: `${results.length} entities seeded and assigned to SUPER ADMIN.`,
        seeded: results.map((r) => r.entity.name),
      };
    } catch (error) {
      console.error('Failed to seed default entities:', error);
      throw new Error('Default entity seeding failed.');
    }
  }
}
