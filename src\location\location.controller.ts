import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Public } from 'src/common/decorators/public.decorator';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateLocationDto } from './dto/create-location.dto';
import { LocationService } from './location.service';

@ApiTags('Location')
@Controller('location')
export class LocationController {
  constructor(private readonly locationService: LocationService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new branch with the provided details.',
    dto: CreateLocationDto,
    statusCodes: [],
  })
  @RequirePrivilege('LOCATION|CREATE')
  @Post('/create')
  createLocation(@Body() payload: CreateLocationDto, @Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.locationService.createLocation({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates bulk location with the provided details.',
    dto: Array<CreateLocationDto>,
    statusCodes: [],
  })
  @RequirePrivilege('LOCATION|CREATE')
  @Post('/create/bulk')
  createBulkLocation(
    @Body() payload: CreateLocationDto[],
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.locationService.createBulkLocation({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all locations.',
    statusCodes: [],
  })
  @RequirePrivilege(['LOCATION|READ', 'BRANCH|CREATE', 'USER|CREATE'], 'any')
  @Get('/read')
  getLocations(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.locationService.getLocations(token!);
  }

  @Public() //Change after dev
  // @RequirePrivilege('LOCATION|RESET')
  @Post('/reset')
  resetLocation() {
    return this.locationService.baseCreateLocation();
  }
}
