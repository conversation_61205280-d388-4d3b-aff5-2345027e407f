import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';

@Module({
  imports: [DatabaseModule],
  providers: [DashboardService, AuthTokenService],
  controllers: [DashboardController],
})
export class DashboardModule {}
