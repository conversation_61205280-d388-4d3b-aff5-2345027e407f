import { Body, Controller, Get, Headers, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { ContractTypeService } from './contract-type.service';
import { CreateContractTypeDto } from './dto/contract-type-status.dto';
import { DeleteContractTypeDto } from './dto/delete-contract-type.dto';
import { UpdateContractTypeDto } from './dto/update-contract-type.dto';

@ApiTags('Contract Type')
@Controller('contract-type')
export class ContractTypeController {
  constructor(private readonly contractTypeService: ContractTypeService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new contractType with the provided details.',
    dto: CreateContractTypeDto,
    statusCodes: [],
  })
  @RequirePrivilege('CONTRACT_TYPE|CREATE')
  @Post()
  createContractType(
    @Body() createContractTypeDto: CreateContractTypeDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.contractTypeService.createContractType({
      payload: createContractTypeDto,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all contract type.',
    statusCodes: [],
  })
  @RequirePrivilege('CONTRACT_TYPE|VIEW')
  @Get()
  getContractTypes(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.contractTypeService.getContractTypes(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update designations.',
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.CONTRACT_TYPE}|${ACTIONS_CONSTANT.update}`,
  )
  @Post('/update')
  updateDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateContractTypeDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.contractTypeService.updateContractTypeRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete designations.',
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.CONTRACT_TYPE}|${ACTIONS_CONSTANT.delete}`,
  )
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteContractTypeDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.contractTypeService.deleteContractTypeRequest({
      payload,
      token: token,
    });
  }
}
