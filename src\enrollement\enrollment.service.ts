import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AccountService } from 'src/account/account.service';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseService } from 'src/database/database.service';
import { OtpService } from 'src/otp/otp.service';
import { CompleteEnrollmentDto } from './dto/complete-enrollment.dto';
import { InitiateEnrollmentDto } from './dto/initiate-enrollment.dto';

@Injectable()
export class EnrollmentService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly otpService: OtpService,
    private readonly cryptoService: CryptoService,
    private readonly accountService: AccountService,
  ) {}

  private generateUniqueRef(prefix = 'ENR'): string {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const random = Math.random().toString(36).substring(2, 8).toUpperCase(); // 6 chars
    return `${prefix}-${date}-${random}`; // e.g., ENR-********-K93LZX
  }

  async initiateEnrollment(payload: InitiateEnrollmentDto) {
    console.log(payload);

    const { password, accountEmail: email, accountName: name } = payload;

    try {
      // Check if the company already exists by email
      const existingCompany = await this.databaseService.account.findUnique({
        where: { email },
      });

      if (existingCompany) {
        throw new BadRequestException(
          'An account with provided email already exists',
        );
      }
      const uniqueRef = this.generateUniqueRef();

      const passwordHash = await this.cryptoService.hash(password);
      // Check if the role already exists by name
      const newEnrollment = await this.databaseService.enrollment.upsert({
        where: { accountEmail: email },
        update: {
          ...payload,

          uniqueRef,
          password: passwordHash,
          accountEmail: email,
          accountName: name,
        },
        create: {
          ...payload,
          uniqueRef,
          password: passwordHash,
          accountEmail: email,
          accountName: name,
        },
      });

      const otp = await this.otpService.requestOtp({
        email,
      });

      return {
        uniqueRef: newEnrollment.uniqueRef,
        otpRef: otp.otpRef,
      };
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async findEnrollment(payload: { uniqueRef: string }) {
    const { uniqueRef } = payload;
    try {
      // Check if the company already exists by email
      const enrollment = await this.databaseService.enrollment.findUnique({
        where: { uniqueRef },
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      return enrollment;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async completeEnrollment(payload: CompleteEnrollmentDto) {
    const { otp, uniqueRef } = payload;

    try {
      // Check if the company already exists by email
      const existingEnrollment = await this.findEnrollment({ uniqueRef });

      if (!existingEnrollment.id) {
        throw new NotFoundException('Enrollment not found');
      }

      // Verify the OTP
      const verifyOtp = await this.otpService.verifyOtp({
        code: otp,
        email: existingEnrollment.accountEmail,
      });

      if (!verifyOtp.otpVerified) {
        throw new BadRequestException('Invalid or expired OTP');
      }
      // Create the company

      const baseSlug = this.accountService.generateSlug(
        existingEnrollment.name,
      );

      const slug = await this.accountService.ensureUniqueSlug(baseSlug);
      // const count = await this.databaseService.account.count();

      await this.databaseService.$transaction(async (tx) => {
        const {
          address,
          email,
          name,
          phoneNumber,
          city,
          country,
          createdAt,
          description,
          industry,
          industryType,
          logo,
          registrationNumber,
          state,
          password,
          website,
          zipCode,
          accountEmail,
          accountName,
        } = existingEnrollment;

        const newAccount = await tx.account.create({
          data: {
            // accountId: ********** + count,
            email: accountEmail,
            name: accountName,
            password,
            slug,
            roleId: 100,
          },
        });

        await tx.company.create({
          data: {
            address,
            accountId: newAccount.id,
            email,
            name,
            phoneNumber,
            city,
            country,
            createdAt,
            description,
            industry,
            industryType,
            logo,
            registrationNumber,
            state,
            website,
            zipCode,
            emailVerified: true,
          },
        });

        await tx.enrollment.delete({
          where: { uniqueRef },
        });
      });

      return {
        slug,
      };
    } catch (error) {
      console.log('Enrollment error', error);

      throw error;
    }
  }
}
