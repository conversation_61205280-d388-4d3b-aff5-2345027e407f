import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString } from 'class-validator';

export class NextOfKinDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: '<PERSON>' })
  nextOfKinFullName?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'Sister' })
  nextOfKinRelationship?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: '+2348012345678' })
  nextOfKinPhoneNumber?: string;

  @IsOptional()
  @IsEmail()
  @ApiPropertyOptional({ example: '<EMAIL>' })
  nextOfKinEmail?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ example: 'No. 5, Unity Street, Lagos' })
  nextOfKinAddress?: string;
}
