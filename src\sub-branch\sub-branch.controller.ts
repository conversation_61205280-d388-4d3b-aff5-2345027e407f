import { Body, Controller, Get, Headers, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateSubBranchDto } from './dto/create-sub-branch.dto';
import { DeleteSubBranchDto } from './dto/delete-sub-branch.dto';
import { UpdateSubBranchDto } from './dto/update-sub-branch.dto';
import { SubBranchService } from './sub-branch.service';

@ApiTags('Sub-branch')
@Controller('sub-branch')
export class SubBranchController {
  constructor(private readonly subBranchService: SubBranchService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new unit with the provided details.',
    dto: CreateSubBranchDto,
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.SUB_BRANCH}|${ACTIONS_CONSTANT.create}`)
  @Post('/create')
  createUnit(@Body() payload: CreateSubBranchDto, @Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.subBranchService.createSubBranch({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all units.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.SUB_BRANCH}|${ACTIONS_CONSTANT.view}`)
  @Get('/read')
  getUnits(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.subBranchService.getSubBranches(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update grade level.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.SUB_BRANCH}|${ACTIONS_CONSTANT.update}`)
  @Post('/update')
  updateUnit(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateSubBranchDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.subBranchService.updateSubBranchRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete unit.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.SUB_BRANCH}|${ACTIONS_CONSTANT.delete}`)
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteSubBranchDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.subBranchService.deleteSubBranchRequest({
      payload,
      token: token,
    });
  }
}
