import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue, Role } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

@Injectable()
export class RoleService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  // Method to find a role by ID or Name
  async findRole({
    identifier,
    token,
  }: {
    identifier: number | string;
    token: string;
  }) {
    let role:
      | (Role & {
          RolePrivilege: {
            privilege: {
              id: string;
              name: string;
              status: string;
            };
          }[];
        })
      | null;

    const decodedToken = await this.authTokenService.decodeToken(token);

    const { companyId } = decodedToken;

    // If identifier is a number, search by id, otherwise by name
    if (typeof identifier === 'number') {
      role = await this.databaseService.role.findUnique({
        where: { id: identifier },
        include: {
          RolePrivilege: {
            select: {
              privilege: {
                select: {
                  id: true,
                  name: true,
                  status: true,
                },
              },
            },
          },
        },
      });
    } else {
      role = await this.databaseService.role.findUnique({
        where: { name: identifier, companyId },
        include: {
          RolePrivilege: {
            select: {
              privilege: {
                select: {
                  id: true,
                  name: true,
                  status: true,
                },
              },
            },
          },
        },
      });
    }

    if (!role) return null;

    const { RolePrivilege, ...roleData } = role;

    return {
      ...roleData,
      privileges: RolePrivilege.map((rp) => rp.privilege),
    };
  }
  // Method to find a role by ID or Name
  async getRoles(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const roles = await this.databaseService.role.findMany({
        where: {
          OR: [
            {
              isPublic: true,
            },
            {
              companyId,
            },
          ],
        },
      });

      return roles;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptRoleAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    // Check if the role already exists by name
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { description, name, privileges } = JSON.parse(
          queue.data,
        ) as CreateRoleDto;

        // Check if the role already exists by name
        const roleExist = await this.databaseService.role.findUnique({
          where: { name, companyId },
        });

        if (roleExist) {
          throw new BadRequestException('Role already exists');
        }
        await this.databaseService.$transaction(async (tx) => {
          const newRole = await tx.role.create({
            data: {
              name,
              description,
              createdBy: requestedBy,
              approvedBy,
              company: {
                connect: {
                  id: companyId,
                },
              },
            },
          });

          await tx.rolePrivilege.createMany({
            data: privileges.map((privilegeName) => ({
              roleId: newRole.id,
              privilegeName,
            })),
          });
        });
        return true;
      }

      case 'UPDATE': {
        const { description, name, privileges, id } = JSON.parse(
          queue.data,
        ) as UpdateRoleDto;

        if (!id) {
          throw new BadRequestException('Role does not exists');
        }

        // Check if the role already exists by name
        const roleExist = await this.databaseService.role.findUnique({
          where: { id },
        });

        if (!roleExist) {
          throw new BadRequestException('Role does not exists');
        }

        await this.databaseService.$transaction(async (tx) => {
          const updatedRole = await tx.role.update({
            where: {
              id: roleExist.id,
            },
            data: {
              name,
              description,
              createdBy: requestedBy,
              approvedBy,
            },
          });

          if (privileges) {
            await tx.rolePrivilege.deleteMany({
              where: { roleId: updatedRole.id },
            });

            const uniquePrivileges = [...new Set(privileges)];

            await tx.rolePrivilege.createMany({
              data: uniquePrivileges.map((privilegeName) => ({
                roleId: updatedRole.id,
                privilegeName,
              })),
            });
          }
        });
        return true;
      }
      default:
        return false;
    }
  }

  async createRole({
    payload,
    token,
  }: {
    payload: CreateRoleDto;
    token: string;
  }) {
    const { name, privileges, description } = payload;

    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken.companyId);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload: {
          name,
          privileges,
          description,
        },
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.ROLE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateRole({
    payload,
    token,
  }: {
    payload: UpdateRoleDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    if (payload.id === 100) {
      throw new BadRequestException('No privilege to modify SUPER_ADMIN Role');
    }

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.ROLE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteRole({
    payload,
    token,
  }: {
    payload: UpdateRoleDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken.companyId);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.ROLE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}
