import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUrl,
} from 'class-validator';

export class AccountDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address for the root user account.',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the root user.',
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 'P@ssw0rd!',
    description: 'Password for the root user.',
  })
  @IsString()
  password: string;
}

export class InitiateEnrollmentDto {
  // ============================
  // 📧 Company Email
  // ============================
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the company (also used for root user).',
  })
  @IsEmail()
  email: string;

  // ============================
  // ☎️ Phone Number
  // ============================
  @ApiProperty({
    example: '+*************',
    description: 'The phone number of the company.',
  })
  @IsPhoneNumber()
  phoneNumber: string; // ✅ should be string, not number

  // ============================
  // 🏢 Company Info
  // ============================
  @ApiProperty({ example: 'Example Corp', description: 'Company name.' })
  @IsString()
  name: string;

  @ApiProperty({
    example: 'https://example.com/logo.png',
    description: 'URL of the company logo.',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  logo?: string;

  @ApiProperty({ example: '123 Example St', description: 'Company address.' })
  @IsString()
  address: string;

  @ApiProperty({ example: 'Lagos', description: 'State of the company.' })
  @IsString()
  state: string;

  @ApiProperty({ example: 'Ikeja', description: 'City of the company.' })
  @IsString()
  city: string;

  @ApiProperty({ example: '100001', description: 'ZIP/postal code.' })
  @IsOptional()
  @IsString()
  zipCode?: string;

  @ApiProperty({
    example: 'https://company.com',
    description: 'Company website URL.',
  })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiProperty({
    example: 'We build amazing software.',
    description: 'Brief description of the company.',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: 'REG123456',
    description: 'Company registration number.',
    required: false,
  })
  @IsOptional()
  @IsString()
  registrationNumber?: string;

  @ApiProperty({
    example: 'Technology',
    description: 'Industry the company belongs to.',
    required: false,
  })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({
    example: 'Software Development',
    description: 'Type of the industry.',
    required: false,
  })
  @IsOptional()
  @IsString()
  industryType?: string;

  @ApiProperty({
    example: 'Nigeria',
    description: 'Country where the company is based.',
  })
  @IsString()
  country: string;

  // ============================
  // 👤 Root User Info
  // ============================
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address for the root user account.',
  })
  @IsEmail()
  accountEmail: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the root user.',
  })
  @IsString()
  accountName: string;

  @ApiProperty({
    example: 'P@ssw0rd!',
    description: 'Password for the root user.',
  })
  @IsString()
  password: string;
}
