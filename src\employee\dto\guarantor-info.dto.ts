// dto/guarantor-info.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class GuarantorInfoDto {
  @ApiProperty({ example: '<PERSON>', required: false })
  @IsOptional()
  @IsString()
  guarantorFullname?: string;

  @ApiProperty({ example: '08011223344', required: false })
  @IsOptional()
  @IsString()
  guarantorPhoneNumber?: string;

  @ApiProperty({ example: 'Friend', required: false })
  @IsOptional()
  @IsString()
  guarantorRelationShip?: string;

  @ApiProperty({ example: 'No 4 Estate Road', required: false })
  @IsOptional()
  @IsString()
  guarantorAddress?: string;

  @ApiProperty({ example: 'Engineer', required: false })
  @IsOptional()
  @IsString()
  guarantorOccupation?: string;

  @ApiProperty({ example: 'Driver’s License', required: false })
  @IsOptional()
  @IsString()
  guarantorMeansOfIdentification?: string;
}
