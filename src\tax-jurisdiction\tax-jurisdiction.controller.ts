import { Body, Controller, Get, Headers, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateBulkTaxJurisdictionDto } from './dto/create-bulk-tax-jurisdiction.dto';
import { CreateTaxJurisdictionDto } from './dto/create-tax-jurisdiction.dto';
import { DeleteTaxJurisdictionDto } from './dto/delete-tax-jurisdiction.dto';
import { UpdateTaxJurisdictionDto } from './dto/update-tax-jurisdiction.dto';
import { TaxJurisdictionService } from './tax-jurisdiction.service';

@ApiTags('Tax Jurisdiction')
@Controller('tax-jurisdiction')
export class TaxjurisdictionController {
  constructor(
    private readonly taxJurisdictionService: TaxJurisdictionService,
  ) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new tax jurisdiction with the provided details.',
    dto: CreateTaxJurisdictionDto,
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.TAX_JURISDICTION}|${ACTIONS_CONSTANT.create}`,
  )
  @Post('create')
  createTaxjurisdiction(
    @Body()
    payload: CreateTaxJurisdictionDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader.split(' ')[1];

    return this.taxJurisdictionService.createTaxJurisdiction({
      payload,
      token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates bulk tax jurisdiction with the provided details.',
    dto: CreateBulkTaxJurisdictionDto,
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.TAX_JURISDICTION}|${ACTIONS_CONSTANT.create}`,
  )
  @Post('bulk/create')
  async createBulk(
    @Body() payload: CreateBulkTaxJurisdictionDto,
    @Headers('Authorization') token: string,
  ) {
    console.log('Tax', token);

    return this.taxJurisdictionService.createBulkTaxJurisdictions({
      payload,
      token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all tax jurisdictions.',
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.TAX_JURISDICTION}|${ACTIONS_CONSTANT.view}`,
  )
  @Get()
  getTaxjurisdictions(@Headers('authorization') authHeader: string) {
    const token = authHeader?.split(' ')[1];

    return this.taxJurisdictionService.getTaxJurisdictions(token);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update grade level.',
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.TAX_JURISDICTION}|${ACTIONS_CONSTANT.update}`,
  )
  @Post('/update')
  updateTaxjurisdiction(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateTaxJurisdictionDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.taxJurisdictionService.updateTaxJurisdictionRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete taxjurisdiction.',
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.TAX_JURISDICTION}|${ACTIONS_CONSTANT.delete}`,
  )
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteTaxJurisdictionDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.taxJurisdictionService.deleteTaxJurisdictionRequest({
      payload,
      token,
    });
  }
}
