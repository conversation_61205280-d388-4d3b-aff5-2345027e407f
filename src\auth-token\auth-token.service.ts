import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthTokenService {
  constructor(private readonly jwtService: JwtService) {}

  // Method to decode the token from the Authorization header
  async decodeToken(token?: string) {
    if (!token) {
      throw new Error('Token not found');
    }

    try {
      // Decode the token (without verifying it)
      const decodedToken: {
        email: string;
        companyId: string;
        accountId: number;
        name: string;
        roleId: number;
        id: string;
        iat: number;
        exp: number;
      } = await this.jwtService.verifyAsync(
        token.split(' ').length > 1 ? token.split(' ')[1] : token,
      );

      console.log(decodedToken);

      return decodedToken;
    } catch (error) {
      console.log(error);

      throw new Error('Failed to decode token');
    }
  }
}
