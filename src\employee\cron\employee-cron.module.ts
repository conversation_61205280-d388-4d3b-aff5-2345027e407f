import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { ValidationService } from 'src/validation/validation.service';
import { EmployeeService } from '../employee.service';
import { EmployeeCronController } from './employee-cron.controller';
import { EmployeeCronService } from './employee-cron.service';

@Module({
  imports: [ScheduleModule.forRoot(), HttpModule],
  providers: [
    EmployeeCronService,
    DatabaseService,
    EmployeeService,
    AuthTokenService,
    ValidationService,
  ],
  controllers: [EmployeeCronController],
})
export class EmployeeCronModule {}
