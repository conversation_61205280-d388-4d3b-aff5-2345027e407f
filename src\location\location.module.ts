import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { AuthorizationModule } from 'src/authorization-queue/authorization-queue.module';
import { DatabaseModule } from 'src/database/database.module';
import { LocationController } from './location.controller';
import { LocationService } from './location.service';

@Module({
  imports: [DatabaseModule, AuthorizationModule],
  providers: [LocationService, AuthTokenService],
  controllers: [LocationController],
})
export class LocationModule {}
