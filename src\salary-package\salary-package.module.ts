import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { SalaryPackageController } from './salary-package.controller';
import { SalaryPackageService } from './salary-package.service';

@Module({
  imports: [DatabaseModule],
  providers: [SalaryPackageService, AuthTokenService],
  controllers: [SalaryPackageController],
})
export class SalaryPackageModule {}
