import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { CreateCompanyDto } from './dto/create-company.dto';

@Injectable()
export class CompanyService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  async findCompany({
    email,
    accountId,
  }: {
    email: string;
    accountId: number;
  }) {
    return this.databaseService.company.findUnique({
      where: {
        accountId_email: {
          email,
          accountId,
        },
      },
    });
  }

  async createCompany({
    payload,
    token,
  }: {
    payload: CreateCompanyDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);
    const { email } = payload;

    const accountExist = await this.databaseService.account.findUnique({
      where: { id: decodedToken.accountId },
    });
    if (!accountExist) {
      throw new BadRequestException('Account not found');
    }

    const companyExist = await this.findCompany({
      email,
      accountId: decodedToken.accountId,
    });
    if (companyExist) {
      throw new BadRequestException('Company with email already exists');
    }

    const newCompany = await this.databaseService.company.create({
      data: {
        ...payload,
        accountId: accountExist.id,
      },
    });

    return newCompany;
  }
}
