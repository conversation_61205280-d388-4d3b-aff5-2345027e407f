import { Module } from '@nestjs/common';
import { AuthorizationModule } from 'src/authorization-queue/authorization-queue.module';
import { DatabaseModule } from 'src/database/database.module';
import { AuthorizationRequestMaker } from './authorization-request.maker';

@Module({
  imports: [DatabaseModule, AuthorizationModule],
  providers: [AuthorizationRequestMaker],
  exports: [AuthorizationRequestMaker],
})
export class AuthorizationRequestMakerModule {}
