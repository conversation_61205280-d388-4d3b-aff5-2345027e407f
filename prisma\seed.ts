import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function main() {
  // Check if Super Admin exists
  const superAdmin = await prisma.role.findUnique({ where: { id: 100 } });

  if (!superAdmin) {
    // Create Super Admin with fixed ID
    await prisma.role.create({
      data: {
        id: 100,
        name: 'SUPER_ADMIN',
        isPublic: true,
        createdBy: 'System',
      },
    });

    // Reset the sequence to start from 101
    await prisma.$executeRawUnsafe(`
      ALTER SEQUENCE "Role_id_seq" RESTART WITH 101;
    `);

    await prisma.$executeRawUnsafe(`
      ALTER SEQUENCE "Account_id_seq" RESTART WITH **********
    `);
  }
}

main()
  .then(async () => {
    console.log('Seeding completed.');
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
