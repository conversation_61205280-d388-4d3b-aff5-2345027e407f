import { BadRequestException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseService } from 'src/database/database.service';
import { OtpService } from 'src/otp/otp.service';
import { AdminAuthDto } from './dto/admin-auth.dto';

@Injectable()
export class AdminAuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cryptoService: CryptoService,
    private jwtService: JwtService,
    private readonly authTokenService: AuthTokenService,
    private readonly otpService: OtpService,
  ) {}

  async signIn(payload: AdminAuthDto) {
    const { email, password } = payload;

    try {
      const userExist = await this.databaseService.adminUser.findUnique({
        where: {
          email: email,
        },
      });

      if (!userExist) {
        throw new BadRequestException('Invalid Credentials');
      }

      const isPassword = await this.cryptoService.compare({
        data: password,
        hash: userExist?.password,
      });

      if (!isPassword) {
        throw new BadRequestException('Invalid Credentials');
      }

      // TODO: Generate a JWT and return it here
      const access_token = await this.jwtService.signAsync(
        {
          email: userExist?.email,
          roleId: userExist?.roleId,
          name: userExist?.name || userExist?.email,
        },
        {
          expiresIn: '24h',
        },
      );

      return {
        ...userExist,
        userId: userExist?.id || null,
        password: '',
        access_token,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
