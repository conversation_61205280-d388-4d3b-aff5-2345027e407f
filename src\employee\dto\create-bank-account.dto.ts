import { IsE<PERSON>, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class CreateBankAccountDto {
  @IsString()
  customerPhoneNumber: string;

  @IsOptional()
  @IsString()
  customerBvn: string;

  @IsEmail()
  customerEmail: string;

  @IsString()
  customerFirstName: string;

  @IsOptional()
  @IsString()
  customerMiddleName?: string;

  @IsString()
  customerLastName: string;

  @IsString()
  customerDob: string; // Use @IsDateString() if using ISO format

  @IsString()
  customerCountryCode: string;

  @IsString()
  accountCurrency: string;

  @IsString()
  customerAccountOfficer: string;

  @IsNumber()
  customerAge: number;

  @IsString()
  customerAddress: string;

  @IsOptional()
  @IsString()
  customerCity?: string;

  @IsString()
  customerState: string;

  @IsString()
  customerCountry: string;

  @IsString()
  accountType?: string;

  @IsString()
  customerType?: string;

  @IsString()
  accountClass: string;

  @IsString()
  accountNumber?: string;

  @IsString()
  customerGender: string;

  @IsString()
  customerNin: string;

  @IsString()
  customerBranchCode: string;

  @IsString()
  customerBankCode: string;

  @IsString()
  customerIncorporationDate: string;

  @IsString()
  customerMaker?: string;

  @IsOptional()
  @IsString()
  customerChecker?: string;

  @IsOptional()
  @IsString()
  customerNextOfKinName?: string;

  @IsOptional()
  @IsString()
  customerNextOfKinPhoneNumber?: string;

  @IsOptional()
  @IsString()
  source?: string;

  @IsOptional()
  @IsString()
  userCountryCode?: string;
}
