// dto/employment-info.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString } from 'class-validator';

export class EmploymentInfoDto {
  @ApiProperty({ example: 1 })
  @IsInt()
  companyId: number;

  @ApiProperty({ example: 1 })
  @IsInt()
  @IsOptional()
  pfaId: number;

  @ApiProperty({ example: 'Developer', required: false })
  @IsOptional()
  @IsString()
  jobTitleId: string;

  @ApiProperty({ example: 'Developer', required: false })
  @IsOptional()
  @IsString()
  jobUnitId: string;

  @ApiProperty({ example: 'Developer', required: false })
  @IsOptional()
  @IsString()
  jobdeptId: string;

  @ApiProperty({ example: '2022-01-01' })
  @IsString()
  dateEmployed: string;

  @ApiProperty({ example: 'PENDING' })
  @IsString()
  status: string;
}
