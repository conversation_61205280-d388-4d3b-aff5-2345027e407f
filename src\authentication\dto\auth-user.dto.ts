import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  Allow,
  IsEmail,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class AuthUserDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt({ message: 'Account ID must be an integer' })
  @IsNotEmpty({ message: 'Account ID must not be empty if provided' })
  accountId?: number;

  @ApiProperty()
  @IsString({ message: 'Company ID must be a string' })
  @IsNotEmpty({ message: 'Company ID is required' })
  companyId: string;

  @ApiProperty({
    example: '<EMAIL>',
  })
  @Allow()
  @IsEmail({}, { message: 'Kindly provide a valid email' })
  @Transform(({ value }) =>
    typeof value === 'string' ? value.trim().toLowerCase() : String(value),
  )
  email: string;

  @ApiProperty({
    example: 'strongpassword@123',
  })
  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  password: string;
}
