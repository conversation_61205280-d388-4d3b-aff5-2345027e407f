import { Modu<PERSON> } from '@nestjs/common';
import { CryptoModule } from 'src/common/crypto/crypto.module';
import { DatabaseModule } from 'src/database/database.module';
import { MailModule } from 'src/mail/mail.module';
import { OtpController } from './otp.controller';
import { OtpService } from './otp.service';

@Module({
  imports: [DatabaseModule, CryptoModule, MailModule],
  providers: [OtpService],
  controllers: [OtpController],
})
export class OtpModule {}
