// src/common/decorators/require-privilege.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const REQUIRE_PRIVILEGE_KEY = 'require_privilege';

export const RequirePrivilege = (
  privileges: string | string[],
  match: 'all' | 'any' = 'all',
) => {
  const privilegeArray = Array.isArray(privileges) ? privileges : [privileges];
  return SetMetadata(REQUIRE_PRIVILEGE_KEY, {
    privileges: privilegeArray,
    match,
  });
};
