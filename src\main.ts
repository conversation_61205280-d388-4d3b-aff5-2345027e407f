import { ValidationPipe } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { ResponseService } from './common/response/response.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'warn', 'debug', 'verbose'],
  });

  const options = new DocumentBuilder()
    .setTitle('API')
    .setDescription('API Description')
    .addBearerAuth()
    .setVersion('1.0')
    .build();

  const document = SwaggerModule.createDocument(app, options);

  // Apply Bearer auth globally in Swagger
  for (const path of Object.values(document.paths)) {
    for (const method of Object.values(path)) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      method.security = [{ bearer: [] }];
    }
  }
  SwaggerModule.setup('api/docs', app, document);

  const responseService = app.get(ResponseService);
  const reflector = app.get(Reflector);
  app.useGlobalInterceptors(
    new ResponseInterceptor(responseService, reflector),
  );
  app.useGlobalFilters(new HttpExceptionFilter(responseService));
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Increase JSON payload size limit
  app.use(bodyParser.json({ limit: '10mb' })); // or '20mb' based on your need
  app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));
  const port = process.env.PORT || 8000;
  // app.setGlobalPrefix("/api/v1");

  app.enableCors();
  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap().catch((error) => console.log(error));
