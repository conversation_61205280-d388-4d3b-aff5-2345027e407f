import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsDateString,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
} from 'class-validator';

// Create a separate DTO for the user
class UserDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'First name of the user',
  })
  @IsAlpha()
  userFirstname: string;

  @ApiProperty({
    example: 'Abdulmaleek',
    description: 'Last name of the user',
  })
  @IsAlpha()
  userLastname: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: "User's email address",
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  userEmail: string;

  @ApiProperty({
    example: 'Test@123',
    description: 'Password for the user account',
  })
  @IsNotEmpty({ message: 'Password is required' })
  userPassword: string;

  @ApiProperty({
    example: '1995-04-28',
    description: 'Date of birth of the user (optional)',
    required: false,
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Please provide a valid date of birth in YYYY-MM-DD format' },
  )
  userDob?: string; // Optional field for date of birth

  @ApiProperty({
    example: '+**********',
    description: "User's phone number",
  })
  @IsPhoneNumber(undefined, { message: 'Please provide a valid phone number' })
  userPhone: string;

  @ApiProperty({
    example: 'Ali',
    description: "User's middle name (optional)",
    required: false,
  })
  @IsOptional()
  @IsAlpha()
  userMiddlename?: string;
}

export class AuthEnrollmentDto {
  @ApiProperty({
    type: UserDto, // Reference to the UserDto
    example: {
      userEmail: '<EMAIL>',
      userFirstname: 'Adams',
      userLastname: 'Abdulmaleek',
      userMiddlename: 'Ali',
      userPhone: '+**********',
      userDob: '1995-04-28',
    },
  })
  user: UserDto;
}
