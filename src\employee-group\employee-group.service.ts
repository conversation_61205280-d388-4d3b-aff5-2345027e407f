import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateEmployeeGroupDto } from './dto/create-employee-group.dto';
import { DeleteEmployeeGroupDto } from './dto/delete-employee-group.dto';
import { UpdateEmployeeGroupDto } from './dto/update-employee-group.dto';

@Injectable()
export class EmployeeGroupService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findEmployeeGroup({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const employeeGroup = await this.databaseService.gradeLevel.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return employeeGroup;
  }

  async findEmployeeGroupById(id: string) {
    const employeeGroup = await this.databaseService.gradeLevel.findUnique({
      where: {
        id,
      },
    });

    return employeeGroup;
  }

  // Method to find a role by ID or Name
  async getEmployeeGroups(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const employeeGroups = await this.databaseService.gradeLevel.findMany({
        where: {
          companyId,
          status: {
            not: 'INACTIVE',
          },
        },
      });

      return employeeGroups.map((employeeGroup) => ({
        ...employeeGroup,
        name: employeeGroup.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptEmployeeGroupAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateEmployeeGroupDto;

        // Check if the group already exists by name
        const employeeGroupExist =
          await this.databaseService.gradeLevel.findUnique({
            where: {
              name_companyId: {
                companyId,
                name: `${companyId}|${name}`,
              },
            },
          });

        if (employeeGroupExist) {
          throw new BadRequestException('Grade level already exists');
        }

        await this.databaseService.gradeLevel.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }
      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateEmployeeGroupDto;

        await this.updateGradeLevel({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteEmployeeGroupDto;

        await this.deleteGradeLevel({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createEmployeeGroup({
    payload,
    token,
  }: {
    payload: CreateEmployeeGroupDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.EMPLOYEE_GROUP,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateGradeLevelRequest({
    payload,
    token,
  }: {
    payload: UpdateEmployeeGroupDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.EMPLOYEE_GROUP,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateGradeLevel({
    payload,
    companyId,
  }: {
    payload: UpdateEmployeeGroupDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Grade Level Id is required.');
      }

      const gradeLevelExist = await this.databaseService.gradeLevel.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!gradeLevelExist) {
        throw new BadRequestException('Grade level not found.');
      }

      const updatedGrade = await this.databaseService.gradeLevel.update({
        where: {
          id: gradeLevelExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : gradeLevelExist.name,
          companyId,
          description: description ? description : name,
        },
      });

      return updatedGrade;
    } catch (error) {
      console.log('Error updating grade level:', error);

      throw error;
    }
  }

  async deleteGradeLevelRequest({
    payload,
    token,
  }: {
    payload: DeleteEmployeeGroupDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const gradeLevel = await this.findEmployeeGroupById(payload.id);

    if (!gradeLevel) throw new BadRequestException('Grade level not found.');

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload: { ...payload, name: gradeLevel.name.split('|')[1] },
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.EMPLOYEE_GROUP,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteGradeLevel({
    payload,
    companyId,
  }: {
    payload: DeleteEmployeeGroupDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Grade Level Id is required.');
      }

      const gradeLevelExist = await this.databaseService.gradeLevel.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!gradeLevelExist) {
        throw new BadRequestException('Grade Level not found.');
      }

      await this.databaseService.gradeLevel.update({
        where: {
          id: gradeLevelExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting grade level:', error);

      throw error;
    }
  }
}
