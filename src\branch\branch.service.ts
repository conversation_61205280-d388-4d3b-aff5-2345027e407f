import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue, Region } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { DeleteBranchDto } from './dto/delete-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';

@Injectable()
export class BranchService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly taxJurisdictionService: TaxJurisdictionService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findBranch({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const branch = await this.databaseService.branch.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });
    return branch;
  }

  async findBranchByName(name: string) {
    const branch = await this.databaseService.branch.findUnique({
      where: {
        name,
      },
    });
    return branch;
  }

  async findBranchById(id: string) {
    const branch = await this.databaseService.branch.findUnique({
      where: {
        id,
      },
    });
    return branch;
  }

  // Method to find a role by ID or Name
  async getBranches(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const branches = await this.databaseService.branch.findMany({
        where: {
          companyId,
          status: {
            not: 'INACTIVE',
          },
        },
        include: {
          taxJurisdiction: {
            select: {
              name: true,
            },
          },
          region: {
            select: {
              name: true,
            },
          },
        },
      });

      return branches.map((branch) => ({
        ...branch,
        name: branch.name.split('|')[1],
        taxJurisdiction: branch.taxJurisdiction?.name.split('|')[1],
        region: branch.region?.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptBranchAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    let regionExist: Region | null = null;
    switch (action) {
      case 'CREATE': {
        const { name, taxJurisdiction, description, region } = JSON.parse(
          queue.data,
        ) as CreateBranchDto;

        if (!taxJurisdiction || !name) {
          throw new BadRequestException('Missing required field');
        }

        // Check if  taxJurisdiction exist

        const taxJurisdictionExist =
          await this.taxJurisdictionService.findTaxJurisdictionByName(
            `${companyId}|${taxJurisdiction}`,
          );

        if (!taxJurisdictionExist) {
          throw new BadRequestException('Tax Jurisdiction does not exist');
        }

        if (region) {
          regionExist = await this.databaseService.region.findFirst({
            where: {
              companyId,
              name: `${companyId}|${region}`,
            },
          });

          if (!regionExist) {
            throw new BadRequestException('Region does not exists');
          }
        }

        // Check if the group already exists by name
        const branchExist = await this.databaseService.branch.findFirst({
          where: {
            companyId,
            name: `${companyId}|${name}`,
          },
        });

        if (branchExist) {
          throw new BadRequestException('Branch already exists');
        }

        await this.databaseService.branch.create({
          data: {
            name: `${companyId}|${name}`,
            companyId,
            regionId: regionExist?.id,
            taxJurisdictionId: taxJurisdictionExist.id,
            description,
            approvedBy,
            createdBy: requestedBy,
          },
        });

        return true;
      }

      case 'UPDATE': {
        const payload = JSON.parse(queue.data) as UpdateBranchDto;

        await this.updateBranch({
          companyId,
          payload,
        });

        return true;
      }

      case 'DELETE': {
        const payload = JSON.parse(queue.data) as DeleteBranchDto;

        await this.deleteBranch({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createBranch({
    payload,
    token,
  }: {
    payload: CreateBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { taxJurisdiction, name, description, region } = payload;

    if (!taxJurisdiction || !name) {
      throw new BadRequestException('Missing required field');
    }

    try {
      await this.authorizationRequestMaker.queueRequest({
        payload: {
          taxJurisdiction,
          name,
          description,
          region,
        },
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.BRANCH,
        requestedBy: decodedToken.name || decodedToken.email,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateBranch({
    payload,
    companyId,
  }: {
    payload: UpdateBranchDto;
    companyId: string;
  }) {
    const { description, taxJurisdiction, name, id, region } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Branch Id is required.');
      }

      if (!taxJurisdiction || !name) {
        throw new BadRequestException('Missing requred field');
      }

      // Check if  taxJurisdiction exist

      const taxJurisdictionExist =
        await this.taxJurisdictionService.findTaxJurisdictionByName(
          `${companyId}|${taxJurisdiction}`,
        );

      if (!taxJurisdictionExist) {
        throw new BadRequestException('Tax Jurisdication does not exist');
      }

      const regionExist = await this.databaseService.region.findFirst({
        where: {
          companyId,
          name: `${companyId}|${region}`,
        },
      });

      if (!regionExist) {
        throw new BadRequestException('Region does not exists');
      }

      const branchExist = await this.databaseService.branch.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!branchExist) {
        throw new BadRequestException('Branch not found.');
      }

      const updatedBranch = await this.databaseService.branch.update({
        where: {
          id: branchExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : branchExist.name,
          companyId,
          regionId: regionExist.id,
          taxJurisdictionId: taxJurisdictionExist.id,
          description: description ? description : name,
        },
      });

      return updatedBranch;
    } catch (error) {
      console.log('Error updating branch:', error);

      throw error;
    }
  }

  async deleteBranch({
    payload,
    companyId,
  }: {
    payload: DeleteBranchDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Branch Id is required.');
      }

      const branchExist = await this.databaseService.branch.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!branchExist) {
        throw new BadRequestException('Branch not found.');
      }

      const deletedBranch = await this.databaseService.branch.update({
        where: {
          id: branchExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return deletedBranch;
    } catch (error) {
      console.log('Error deleting branch:', error);

      throw error;
    }
  }

  async deleteBranchRequest({
    payload,
    token,
  }: {
    payload: DeleteBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const branchExist = await this.findBranchById(payload.id);

    if (!branchExist) throw new BadRequestException('Branch not found');

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload: { ...payload, name: branchExist.name.split('|')[1] },
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.BRANCH,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateBranchRequest({
    payload,
    token,
  }: {
    payload: UpdateBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.BRANCH,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}
