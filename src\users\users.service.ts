import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { MailService } from 'src/mail/mail.service';
import { CreateUserDto } from './dto/create-user.dto';
import { DeleteUserDto } from './dto/delete-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cryptoService: CryptoService,
    private readonly authTokenService: AuthTokenService,
    private readonly mailService: MailService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}
  async createUser({
    payload,
    companyId,
    approvedBy,
    createdBy,
  }: {
    payload: CreateUserDto;
    companyId: string;
    approvedBy: string;
    createdBy: string;
  }) {
    const {
      email,
      password,
      twoFactorEnabled,
      isRoot,
      roleId,
      branches,
      hasAccessToAllBranches,
      name,
    } = payload;

    try {
      if (!isRoot && !companyId) {
        throw new BadRequestException(
          'Account Id is required for non-root users.',
        );
      }
      if (!roleId) {
        throw new BadRequestException('Role is required for user creation.');
      }
      const userExist = await this.databaseService.user.findUnique({
        where: {
          email_companyId: {
            companyId,
            email,
          },
        },
      });

      if (userExist) {
        throw new BadRequestException('User with this email already exists.');
      }

      const companyExist = await this.databaseService.company.findUnique({
        where: {
          id: companyId,
        },
      });

      if (!companyExist) {
        throw new BadRequestException('Company not found.');
      }

      if (branches && branches.length > 0) {
        const existingBranches = await this.databaseService.branch.findMany({
          where: {
            id: { in: branches },
          },
          select: { id: true },
        });

        const existingBranchIds = existingBranches.map((b) => b.id);

        const invalidBranchIds = branches.filter(
          (id) => !existingBranchIds.includes(id),
        );

        if (invalidBranchIds.length > 0) {
          throw new BadRequestException(
            `Invalid branch IDs: ${invalidBranchIds.join(', ')}`,
          );
        }
        console.log(existingBranchIds);
      }

      const passwordHash = await this.cryptoService.hash(password);

      const newUser = await this.databaseService.user.create({
        data: {
          email,
          password: passwordHash,
          twoFactorEnabled,
          hasAccessToAllBranches,
          isRoot,
          companyId,
          roleId,
          name,
          createdBy,
          status: 'NEW',
          approvedBy,
        },
      });

      const userBranches = branches.map((branchId) => ({
        branchId: branchId,
        userId: newUser.id,
      }));
      await this.databaseService.userBranch.createMany({
        data: userBranches,
      });

      await this.mailService.sendEmail({
        subject: `${companyExist.name} payroll management system`,
        html: `<h1>Login Credentials</h1><p>Company Account ID: ${companyExist.accountId}</p><p>Your Email: ${email}</p><p>Your Password: ${password}</p>`,
        context: {
          name: 'John Doe',
        },
        email: newUser.email,
      });

      return newUser;
    } catch (error) {
      console.log('Error creating user:', error);

      throw error;
    }
  }

  async findUserById(id: string) {
    if (!id) {
      throw new BadRequestException('User Id is required.');
    }
    return await this.databaseService.user.findUnique({
      where: {
        id,
      },
    });
  }

  async updateUser({
    payload,
    companyId,
  }: {
    payload: UpdateUserDto;
    companyId: string;
  }) {
    const {
      email,
      twoFactorEnabled,
      isRoot,
      roleId,
      branches,
      hasAccessToAllBranches,
      name,
      id,
    } = payload;

    try {
      if (!id) {
        throw new BadRequestException('User Id is required.');
      }
      if (!roleId) {
        throw new BadRequestException('Role is required.');
      }
      const userExist = await this.databaseService.user.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!userExist) {
        throw new BadRequestException('User not found.');
      }

      if (branches && branches.length > 0) {
        const existingBranches = await this.databaseService.branch.findMany({
          where: {
            id: { in: branches },
          },
          select: { id: true },
        });

        const existingBranchIds = existingBranches.map((b) => b.id);

        const invalidBranchIds = branches.filter(
          (id) => !existingBranchIds.includes(id),
        );

        if (invalidBranchIds.length > 0) {
          throw new BadRequestException(
            `Invalid branch IDs: ${invalidBranchIds.join(', ')}`,
          );
        }
      }

      const updatedUser = await this.databaseService.$transaction(
        async (prisma) => {
          // Update the User record
          const user = await prisma.user.update({
            where: { id: userExist.id },
            data: {
              email,
              twoFactorEnabled,
              hasAccessToAllBranches,
              isRoot,
              roleId,
              name,
            },
          });

          if (branches !== undefined && branches !== null) {
            // Delete all existing UserBranch entries for this user
            await prisma.userBranch.deleteMany({
              where: { userId: user.id },
            });

            if (branches.length > 0) {
              const userBranchesToCreate = branches.map((branchId) => ({
                branchId: branchId,
                userId: user.id,
              }));
              await prisma.userBranch.createMany({
                data: userBranchesToCreate,
              });
            }
          }

          return user;
        },
      );

      return updatedUser;
    } catch (error) {
      console.log('Error updating user:', error);

      throw error;
    }
  }

  async deleteUser({
    payload,
    companyId,
  }: {
    payload: DeleteUserDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('User Id is required.');
      }

      const userExist = await this.databaseService.user.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!userExist) {
        throw new BadRequestException('User not found.');
      }

      const deletedUser = await this.databaseService.user.update({
        where: {
          id: userExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return deletedUser;
    } catch (error) {
      console.log('Error updating user:', error);

      throw error;
    }
  }

  async getUsers(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const users = await this.databaseService.user.findMany({
        where: {
          companyId,
          status: {
            not: 'INACTIVE',
          },
        },
        include: {
          role: {
            select: {
              name: true,
            },
          },
          branches: {
            select: {
              branch: {
                select: {
                  name: true,
                  id: true,
                },
              },
            },
          },
        },
      });

      return users.map((user) => ({
        ...user,
        password: '',
        branches: user.branches.map(({ branch }) => ({
          ...branch,
          name: branch.name.split('|')[1],
        })),
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async createUserRequest({
    payload,
    token,
  }: {
    payload: CreateUserDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.USER,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteUserRequest({
    payload,
    token,
  }: {
    payload: DeleteUserDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const userExist = await this.findUserById(payload.id);

    if (!userExist) throw new BadRequestException('User not found');

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload: { ...payload, name: userExist.name, email: userExist.email },
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.USER,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateUserRequest({
    payload,
    token,
  }: {
    payload: UpdateUserDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.USER,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async acceptUserAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const payload = JSON.parse(queue.data) as CreateUserDto;

        await this.createUser({
          approvedBy,
          companyId,
          createdBy: requestedBy,
          payload,
        });

        return true;
      }
      case 'UPDATE': {
        const payload = JSON.parse(queue.data) as UpdateUserDto;

        await this.updateUser({
          companyId,
          payload,
        });

        return true;
      }
      case 'DELETE': {
        const payload = JSON.parse(queue.data) as DeleteUserDto;

        await this.deleteUser({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }
}
