// src/common/response/response.service.ts
import { Injectable } from '@nestjs/common';
import { PaginationMetaDto } from './dto/pagination-meta-dto';

interface Meta {
  pagination: PaginationMetaDto;
  [key: string]: any;
}

interface SuccessResponse<T> {
  success: true;
  message: string;
  data: T;
  meta?: Meta;
  timestamp?: string;
}

interface ErrorResponse {
  success: false;
  statusCode: number;
  message: string;
  errors?: Record<string, unknown> | string[];
}

@Injectable()
export class ResponseService {
  success<T>(
    data: T,
    message = 'Request successful',
    meta?: Meta,
  ): SuccessResponse<T> {
    return {
      success: true,
      message,
      data,
      ...(meta && { meta }),
      timestamp: new Date().toISOString(),
    };
  }

  paginated<T>(
    data: T[],
    meta: Meta,
    message = 'Paginated results retrieved successfully',
  ): SuccessResponse<T[]> {
    return {
      success: true,
      message,
      data,
      meta,
      timestamp: new Date().toISOString(),
    };
  }

  error(
    message: string,
    code = 400,
    errors?: Record<string, unknown> | string[],
  ): ErrorResponse {
    return {
      success: false,
      statusCode: code,
      message,
      ...(errors && { errors }),
    };
  }
}
