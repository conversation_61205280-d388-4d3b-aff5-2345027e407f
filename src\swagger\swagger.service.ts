import { Type } from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiResponseOptions,
} from '@nestjs/swagger';

export class SwaggerService {
  static applyOperation({
    description,
    dto,
    method,
    statusCodes,
    params = [],
  }: {
    method: 'create' | 'update' | 'delete' | 'get';
    description: string;
    dto?: Type<any>;
    statusCodes: Array<ApiResponseOptions>;
    params?: Array<{
      name: string;
      description: string;
      type?: any;
      example?: any;
    }>;
  }) {
    const responses = statusCodes.map((statusCode) =>
      ApiResponse({
        ...statusCode,
      }),
    );

    return function (
      target: object,
      key: string,
      descriptor: PropertyDescriptor,
    ) {
      ApiOperation({
        summary: `${method.charAt(0).toUpperCase() + method.slice(1)}`,
        description: description,
      })(target, key, descriptor);

      if (dto && (method === 'create' || method === 'update')) {
        ApiBody({
          description: `${method.charAt(0).toUpperCase() + method.slice(1)} data`,
          type: dto,
        })(target, key, descriptor);
      }

      // Apply route parameter decorators (e.g., slug)
      params.forEach((param) => {
        ApiParam(param)(target, key, descriptor);
      });

      responses.forEach((response) => response(target, key, descriptor));
    };
  }
}
