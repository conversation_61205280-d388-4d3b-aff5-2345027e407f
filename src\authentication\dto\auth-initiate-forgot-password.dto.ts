import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsEmail, IsInt, IsOptional, IsString } from 'class-validator';

export class AuthInitiateForgotPasswordDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  accountId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiProperty()
  @Allow()
  @IsEmail({}, { message: 'Kindly provide a valid email' })
  email: string;
}
