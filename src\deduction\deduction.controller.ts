import { Body, Controller, Get, Param, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import {
  ReadRateLimit,
  SensitiveRateLimit,
} from 'src/common/decorators/rate-limit.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { DeductionService } from './deduction.service';
import { CreateDeductionDto } from './dto/create-deduction.dto';

@ApiTags('Deduction')
@Controller('deduction')
export class DeductionController {
  constructor(private readonly deductionService: DeductionService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new deduction with the provided details.',
    dto: CreateDeductionDto,
    statusCodes: [],
  })
  @SensitiveRateLimit()
  @Post('/create')
  createDeduction(
    @Body() payload: CreateDeductionDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.deductionService.createDeduction({
      payload,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all deductions.',
    statusCodes: [],
  })
  @ReadRateLimit()
  @Get('/read')
  getDeductions(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.deductionService.getDeductions(token!);
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all deductions by grade level id.',
    statusCodes: [],
  })
  @ReadRateLimit()
  @Get('/read/by-grade-level/:gradeLevelId')
  getDeductionsByGradeLevelId(
    @Req() request: Request,
    @Param('gradeLevelId') gradeLevelId: string,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.deductionService.getDeductionsByGradeLevelId({
      token: token!,
      gradeLevelId,
    });
  }
}
