import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsString, MaxLength } from 'class-validator';

export class CreateEntityActionDto {
  @ApiProperty({
    example: 1,
    description: 'The ID of the entity/module this action belongs to.',
  })
  @IsInt()
  entityId: number;

  @ApiProperty({
    example: 'STAFF_CREATE',
    description:
      'The unique action name for this entity (e.g. STAFF_CREATE, LOAN_APPROVE).',
  })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    example: 'Create new staff profile',
    description: 'A human-readable label or description of the action.',
  })
  @IsString()
  @MaxLength(255)
  label: string;
}
