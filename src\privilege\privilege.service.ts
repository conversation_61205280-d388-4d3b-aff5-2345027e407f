import { Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';

@Injectable()
export class PrivilegeService {
  constructor(private readonly databaseService: DatabaseService) {}

  async readPrivileges() {
    try {
      const privileges = await this.databaseService.privilege.findMany({
        where: {
          isPrivate: false,
        },
      });

      return privileges;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }
}
