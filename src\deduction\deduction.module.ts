import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { AuthorizationModule } from 'src/authorization-queue/authorization-queue.module';
import { DatabaseModule } from 'src/database/database.module';
import { DeductionController } from './deduction.controller';
import { DeductionService } from './deduction.service';

@Module({
  imports: [DatabaseModule, AuthorizationModule],
  providers: [DeductionService, AuthTokenService],
  controllers: [DeductionController],
})
export class DeductionModule {}
