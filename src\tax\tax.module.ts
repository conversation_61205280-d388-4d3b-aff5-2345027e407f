import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { TaxController } from './tax.controller';
import { TaxService } from './tax.service';

@Module({
  imports: [DatabaseModule],
  providers: [TaxService, AuthTokenService],
  controllers: [TaxController],
})
export class TaxModule {}
