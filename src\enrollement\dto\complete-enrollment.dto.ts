import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CompleteEnrollmentDto {
  @ApiProperty({
    example: 'ENR-20240421-K93LZX',
    description: 'The unique ref sent on initiate enrollment.',
  })
  @IsString()
  uniqueRef: string;

  @ApiProperty({
    example: '233445',
    description: 'OTP sent to the company email.',
  })
  @IsString()
  otp: string;
}
