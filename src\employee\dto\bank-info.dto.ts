// dto/bank-info.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class BankInfoDto {
  @ApiProperty({ example: 'Corestep MFB' })
  @IsString()
  bankName: string;

  @ApiProperty({ example: '044', required: false })
  @IsOptional()
  @IsString()
  bankSortCode?: string;

  @ApiProperty({ example: '**********', required: false })
  @IsOptional()
  @IsString()
  accountNumber?: string;

  @ApiProperty({ example: '<PERSON> Doe', required: false })
  @IsOptional()
  @IsString()
  accountName?: string;

  @ApiProperty({ example: 'PENDING' })
  @IsString()
  accountStatus: string;
}
