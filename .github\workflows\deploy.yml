name: Deploy to LINUX cloud server

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
    - name: Checkout Repository
      uses: actions/checkout@v2

    - name: Set up SSH key
      uses: webfactory/ssh-agent@v0.5.1
      with:
        ssh-private-key: ${{ secrets.UAT_ONE_KEY }}

    - name: Deploy to SERVER
      run: |
        echo "Deploying to Server"
        
        # Variables
        DESTINATION_PATH=${{ secrets.PAYROLL_API_DESTINATION }}
        SERVER_HOSTNAME=${{ secrets.UAT_ONE_HOST }}
        SERVER_USERNAME=${{ secrets.UAT_ONE_USER }}
      
        echo "Coping application to server"
        
        rsync -avz --delete-after --exclude 'node_modules' --exclude '.env' -e "ssh -o StrictHostKeyChecking=no" ./ $SERVER_USERNAME@$SERVER_HOSTNAME:$DESTINATION_PATH
        
        echo "Install dependencies"
        
        ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SERVER_USERNAME@$SERVER_HOSTNAME "
          cd $DESTINATION_PATH &&
          if [ -f package-lock.json ] && ! cmp -s package-lock.json package-lock.json; then
              echo 'Dependencies updated, running yarn install';
              yarn install;
          else
              echo 'No updates to dependencies';
          fi &&
          yarn &&
          pm2 restart payroll-api
         "
        echo "Deployment completed successfully"
    
    - name: Finish workflow
      run: exit 0
