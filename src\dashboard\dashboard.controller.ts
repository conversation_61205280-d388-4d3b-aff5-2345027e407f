import { Controller, Get, Query, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ReadRateLimit } from 'src/common/decorators/rate-limit.decorator';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { DashboardService } from './dashboard.service';

@ApiTags('Dashboard')
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}
  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Read dashboard analytics.',
    statusCodes: [],
  })
  @ReadRateLimit()
  @RequirePrivilege('DASHBOARD|VIEW')
  @Get('/read-analytics')
  createSalaryPackage(
    @Query() payload: { period: string },
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.dashboardService.dashboardAnalytics({
      token: token!,
    });
  }
}
