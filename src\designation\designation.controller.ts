import { Body, Controller, Get, Headers, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { DesignationService } from './designation.service';
import { DeleteDesignationDto } from './dto/delete-designation.dto';
import { CreateDesignationDto } from './dto/designation.dto';
import { UpdateDesignationDto } from './dto/update-designation.dto';

@ApiTags('Designation')
@Controller('designation')
export class DesignationController {
  constructor(private readonly designationService: DesignationService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new designation with the provided details.',
    dto: CreateDesignationDto,
    statusCodes: [],
  })
  @RequirePrivilege('DESIGNATION|CREATE')
  @Post()
  creatDesignation(
    @Body() designation: CreateDesignationDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.designationService.createDesignation({
      payload: designation,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all designations.',
    statusCodes: [],
  })
  @RequirePrivilege('DESIGNATION|VIEW')
  @Get()
  getDesignations(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.designationService.getDesignations(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update designations.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.DESIGNATION}|${ACTIONS_CONSTANT.update}`)
  @Post('/update')
  updateDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateDesignationDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.designationService.updateDesignationRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete designations.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.DESIGNATION}|${ACTIONS_CONSTANT.delete}`)
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteDesignationDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.designationService.deleteDesignationRequest({
      payload,
      token: token,
    });
  }
}
