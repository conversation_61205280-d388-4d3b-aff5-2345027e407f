import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  AdminRateLimit,
  PublicRateLimit,
} from 'src/common/decorators/rate-limit.decorator';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { EntityService } from './entity.service';

@ApiTags('Entity')
@Controller('entity')
export class EntityController {
  constructor(private readonly entitEntityServices: EntityService) {}

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all entities',
    statusCodes: [],
  })
  @AdminRateLimit()
  @Get()
  getEntities() {
    return this.entitEntityServices.getEntities();
  }
  @AdminRateLimit()
  @Post()
  createEntity(@Body() payload: CreateEntityDto) {
    return this.entitEntityServices.createEntity(payload);
  }
  @PublicRateLimit()
  @Public()
  @Post('/seed')
  seedEntity() {
    return this.entitEntityServices.seedDefaultEntities();
  }
}
