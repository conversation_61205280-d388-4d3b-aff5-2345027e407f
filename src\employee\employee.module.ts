import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { CryptoModule } from 'src/common/crypto/crypto.module';
import { DatabaseModule } from 'src/database/database.module';
import { ValidationService } from 'src/validation/validation.service';
import { EmployeeCronModule } from './cron/employee-cron.module';
import { EmployeeController } from './employee.controller';
import { EmployeeService } from './employee.service';

@Module({
  imports: [DatabaseModule, CryptoModule, HttpModule, EmployeeCronModule],
  providers: [EmployeeService, AuthTokenService, ValidationService],
  controllers: [EmployeeController],
})
export class EmployeeModule {}
