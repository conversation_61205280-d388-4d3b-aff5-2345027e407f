import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { DepartmentController } from './department.controller';
import { DepartmentService } from './department.service';

@Module({
  imports: [DatabaseModule],
  providers: [DepartmentService, AuthTokenService],
  controllers: [DepartmentController],
})
export class DepartmentModule {}
