import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { CompanyController } from './company.controller';
import { CompanyService } from './company.service';

@Module({
  imports: [DatabaseModule],
  providers: [CompanyService, AuthTokenService],
  controllers: [CompanyController],
})
export class CompanyModule {}
