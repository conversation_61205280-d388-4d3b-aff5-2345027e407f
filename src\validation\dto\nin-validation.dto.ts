import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsInt, IsString } from 'class-validator';

export class NinValidationDto {
  @ApiProperty({
    example: 63184876213,
  })
  @Allow()
  @IsInt({ message: 'Kindly provide a valid 11 digit number' })
  nin: string;

  @ApiProperty({
    example: 'Bunch',
  })
  @Allow()
  @IsString({ message: 'Kindly provide a valid first name' })
  firstname: string;

  @ApiProperty({
    example: 'Dillon',
  })
  @Allow()
  @IsString({ message: 'Kindly provide a valid first name' })
  lastname: string;
}
