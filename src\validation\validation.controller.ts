import { Body, Controller, Post } from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { PublicRateLimit } from 'src/common/decorators/rate-limit.decorator';
import { Public } from 'src/common/decorators/public.decorator';
import { BvnValidationDto } from './dto/bvn-validation.dto';
import { NinValidationDto } from './dto/nin-validation.dto';
import { ValidationService } from './validation.service';

@ApiTags('kyc')
@Controller('kyc')
export class ValidationController {
  constructor(private readonly validationService: ValidationService) {}

  @PublicRateLimit()
  @Public()
  @ApiBody({ type: BvnValidationDto })
  @Post('verify/bvn')
  validateBvn(@Body() payload: BvnValidationDto) {
    return this.validationService.validateBvn(payload);
  }

  @PublicRateLimit()
  @Public()
  @ApiBody({ type: NinValidationDto })
  @Post('verify/nin')
  validateNin(@Body() payload: NinValidationDto) {
    return this.validationService.validateNin(payload);
  }
}
