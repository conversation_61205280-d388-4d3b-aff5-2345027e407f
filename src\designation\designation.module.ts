import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { DesignationController } from './designation.controller';
import { DesignationService } from './designation.service';

@Module({
  imports: [DatabaseModule],
  providers: [DesignationService, AuthTokenService],
  controllers: [DesignationController],
})
export class DesignationModule {}
