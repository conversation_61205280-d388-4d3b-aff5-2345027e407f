import { ApiProperty } from '@nestjs/swagger';
import {
  A<PERSON>yNotEmpty,
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateRoleDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    example: ['string'],
    description: 'Array of permission IDs to assign to the role',
  })
  @IsArray()
  @ArrayNotEmpty()
  privileges: string[];
}
