import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { CreateSalaryPackageDto } from './create-salary-package.dto';

export class BulkCreateSalaryPackageDto {
  @ApiProperty({ type: [CreateSalaryPackageDto] })
  @ValidateNested({ each: true })
  @Type(() => CreateSalaryPackageDto)
  salaryPackages: CreateSalaryPackageDto[];
}
