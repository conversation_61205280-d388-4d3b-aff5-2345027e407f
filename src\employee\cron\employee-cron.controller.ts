import { <PERSON>, <PERSON> } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { EmployeeCronService } from './employee-cron.service';

@ApiTags('Employee Cron')
@Controller('employee-cron')
export class EmployeeCronController {
  constructor(private readonly cronService: EmployeeCronService) {}

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Trigger cron job manually to handle pending bulk jobs',
    statusCodes: [],
  })
  @Public()
  @Post('test')
  async triggerCronManually() {
    return this.cronService.handlePendingBulkJobs();
  }
}
