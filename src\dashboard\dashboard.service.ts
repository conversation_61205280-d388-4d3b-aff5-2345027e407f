import { Injectable } from '@nestjs/common';
import { format, subMonths } from 'date-fns';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';

@Injectable()
export class DashboardService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  async dashboardAnalytics({ token }: { token: string }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Generate last 12 months as ['2024-06', '2024-05', ..., '2023-07']
    const months = Array.from({ length: 12 }, (_, i) =>
      format(subMonths(new Date(), i), 'yyyy-MM'),
    );

    const totalEmployeee = await this.databaseService.employee.count({
      where: { companyId: decodedToken.companyId, status: 'ACTIVE' },
    });

    const totalBranches = await this.databaseService.branch.count({
      where: { companyId: decodedToken.companyId },
    });

    const pendingApprovals =
      await this.databaseService.authorizationQueue.count({
        where: { companyId: decodedToken.companyId, status: 'PENDING' },
      });

    // Get all payrolls for the last 12 months
    const payrolls = await this.databaseService.payrollUpload.findMany({
      where: {
        companyId: decodedToken.companyId,
        period: {
          in: months,
        },
      },
      select: {
        id: true,
        period: true,
      },
    });

    const payrollIdToPeriodMap = Object.fromEntries(
      payrolls.map(({ id, period }) => [id, period]),
    );

    const payrollIds = payrolls.map((p) => p.id);

    // const payrollSums = await this.databaseService.payrollRecordUpload.groupBy({
    //   by: ['payrollId'],
    //   where: {
    //     payrollId: { in: payrollIds },
    //   },
    //   _sum: {
    //     basicSalary: true,
    //     allowances: true,
    //     deductions: true,
    //     tax: true,
    //     netPay: true,
    //   },
    // });

    // Merge and reduce data by month
    const breakdownByMonth = months.reduce(
      (acc, month) => {
        acc[month] = {
          basicSalary: 0,
          allowances: 0,
          deductions: 0,
          tax: 0,
          netPay: 0,
        };
        return acc;
      },
      {} as Record<
        string,
        {
          basicSalary: number;
          allowances: number;
          deductions: number;
          tax: number;
          netPay: number;
        }
      >,
    );

    // for (const group of payrollSums) {
    //   const period = payrollIdToPeriodMap[group.payrollId];
    //   if (!period || !breakdownByMonth[period]) continue;

    //   breakdownByMonth[period].basicSalary += group._sum.basicSalary || 0;
    //   breakdownByMonth[period].allowances += group._sum.allowances || 0;
    //   breakdownByMonth[period].deductions += group._sum.deductions || 0;
    //   breakdownByMonth[period].tax += group._sum.tax || 0;
    //   breakdownByMonth[period].netPay += group._sum.netPay || 0;
    // }

    return {
      pendingApprovals,
      totalEmployeee,
      totalBranches,
      monthlyBreakdown: breakdownByMonth, // Sorted from current month backwards
    };
  }
}
