import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { OTPRateLimit } from 'src/common/decorators/rate-limit.decorator';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { RequestOtpDto } from './dto/request-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { OtpService } from './otp.service';

@ApiTags('OTP')
@Controller('otp')
export class OtpController {
  constructor(private readonly OtpService: OtpService) {}

  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Request OTP',
    dto: RequestOtpDto,
    statusCodes: [],
  })
  @OTPRateLimit()
  @Post('request')
  @Public()
  requestOtp(@Body() payload: RequestOtpDto) {
    return this.OtpService.requestOtp(payload);
  }
  @OTPRateLimit()
  @Post('verify')
  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Request OTP',
    dto: VerifyOtpDto,
    statusCodes: [],
  })
  createEntity(@Body() payload: VerifyOtpDto) {
    return this.OtpService.verifyOtp(payload);
  }
}
