// src/common/guards/privilege.guard.ts
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { REQUIRE_PRIVILEGE_KEY } from '../decorators/require-privilege.decorator';
import { Request } from 'express';

@Injectable()
export class PrivilegeGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private authTokenService: AuthTokenService,
    private databaseService: DatabaseService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const meta = this.reflector.get<{
      privileges: string[];
      match: 'any' | 'all';
    }>(REQUIRE_PRIVILEGE_KEY, context.getHandler());

    if (!meta) return true;

    const { privileges: requiredPrivileges, match } = meta;

    const request: Request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('Missing Authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = await this.authTokenService.decodeToken(token);
    const userId = decoded.id;
    const accountId = decoded.accountId;

    console.log(String(accountId) === String(userId));

    const user =
      String(accountId) === String(userId)
        ? await this.databaseService.account.findUnique({
            where: { id: accountId },
            include: {
              role: {
                include: {
                  RolePrivilege: true,
                },
              },
            },
          })
        : await this.databaseService.user.findUnique({
            where: { id: userId },
            include: {
              role: {
                include: {
                  RolePrivilege: true,
                },
              },
            },
          });

    if (!user || !user.role) {
      throw new ForbiddenException('User or role not found');
    }

    const userPrivileges = user.role.RolePrivilege.map(
      (rp) => rp.privilegeName,
    );

    const hasPrivileges =
      match === 'all'
        ? requiredPrivileges.every((priv) => userPrivileges.includes(priv))
        : requiredPrivileges.some((priv) => userPrivileges.includes(priv));

    if (!hasPrivileges) {
      throw new ForbiddenException('Permission denied');
    }

    return true;
  }
}
