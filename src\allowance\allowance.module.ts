import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { AuthorizationModule } from 'src/authorization-queue/authorization-queue.module';
import { DatabaseModule } from 'src/database/database.module';
import { AllowanceController } from './allowance.controller';
import { AllowanceService } from './allowance.service';

@Module({
  imports: [DatabaseModule, AuthorizationModule],
  providers: [AllowanceService, AuthTokenService],
  controllers: [AllowanceController],
})
export class AllowanceModule {}
