import { Controller, Get, Param, Query, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { SwaggerService } from 'src/swagger/swagger.service';
import { TaxService } from './tax.service';

@ApiTags('Tax')
@Controller('tax')
export class TaxController {
  constructor(private readonly taxService: TaxService) {}
  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get tax reports for a specific branch and payroll.',
    statusCodes: [],
  })
  @Get('/read')
  getTaxReports(
    @Req() request: Request,
    @Param('payrollId') payrollId: string,
    @Query('branchName') branchName: string,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.taxService.getTaxReports({
      token: token!,
      branchName,
      payrollId,
    });
  }
}
