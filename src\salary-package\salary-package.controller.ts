import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import {
  ReadRateLimit,
  SensitiveRateLimit,
  BulkOperationRateLimit,
} from 'src/common/decorators/rate-limit.decorator';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { BulkCreateSalaryPackageDto } from './dto/bulk-create-salary-package.dto';
import { CreateSalaryPackageDto } from './dto/create-salary-package.dto';
import { SalaryPackageService } from './salary-package.service';

@ApiTags('Salary Package')
@Controller('salary-package')
export class SalaryPackageController {
  constructor(private readonly salaryPackageService: SalaryPackageService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new department with the provided details.',
    dto: CreateSalaryPackageDto,
    statusCodes: [],
  })
  @SensitiveRateLimit()
  @RequirePrivilege('SALARY_PACKAGE|CREATE')
  @Post('/create')
  createSalaryPackage(
    @Body() payload: CreateSalaryPackageDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.salaryPackageService.createSalaryPackage({
      payload,
      token: token!,
    });
  }

  // Bulk create salary packages
  @SwaggerService.applyOperation({
    method: 'create',
    description:
      'Bulk creation of new salary packages with the provided details.',
    dto: BulkCreateSalaryPackageDto,
    statusCodes: [],
  })
  @BulkOperationRateLimit()
  @RequirePrivilege('SALARY_PACKAGE|CREATE')
  @Post('create/bulk')
  createBulkSalaryPackage(
    @Req() request: Request,
    @Body() payload: BulkCreateSalaryPackageDto,
  ) {
    const token = request.headers.authorization?.split(' ')[1];
    return this.salaryPackageService.createBulkSalaryPackage({
      data: payload,
      token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all salary packages.',
    statusCodes: [],
  })
  @ReadRateLimit()
  @RequirePrivilege('SALARY_PACKAGE|VIEW')
  @Get('/read')
  getDepartments(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.salaryPackageService.getSalaryPackages(token!);
  }
}
