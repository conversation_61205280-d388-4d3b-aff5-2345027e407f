// src/common/common.module.ts
import { Global, Module } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { AuthorizationRequestMaker } from './maker/authorization-request.maker';
import { ResponseService } from './response/response.service';

@Global()
@Module({
  providers: [ResponseService, AuthorizationRequestMaker, DatabaseService],
  exports: [ResponseService, AuthorizationRequestMaker],
})
export class CommonModule {}
