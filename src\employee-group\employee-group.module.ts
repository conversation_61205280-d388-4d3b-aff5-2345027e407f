import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { EmployeeGroupController } from './employee-group.controller';
import { EmployeeGroupService } from './employee-group.service';

@Module({
  imports: [DatabaseModule],
  providers: [EmployeeGroupService, AuthTokenService],
  controllers: [EmployeeGroupController],
})
export class EmployeeGroupModule {}
