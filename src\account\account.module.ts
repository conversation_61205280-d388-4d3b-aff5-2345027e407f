import { Module } from '@nestjs/common';
import { CryptoModule } from 'src/common/crypto/crypto.module';
import { DatabaseModule } from 'src/database/database.module';
import { AccountController } from './account.controller';
import { AccountService } from './account.service';

@Module({
  imports: [DatabaseModule, CryptoModule],
  providers: [AccountService],
  controllers: [AccountController],
})
export class AccountModule {}
