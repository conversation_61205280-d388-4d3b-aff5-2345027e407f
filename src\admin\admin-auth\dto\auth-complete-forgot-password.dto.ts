import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsEmail, IsInt, IsOptional, IsString } from 'class-validator';

export class AuthCompleteForgotPasswordDto {
  @ApiProperty()
  @IsOptional()
  @IsInt()
  accountId?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiProperty()
  @Allow()
  @IsEmail({}, { message: 'Kindly provide a valid email' })
  email: string;

  @ApiProperty()
  password: string;

  @ApiProperty()
  otp: string;
}
