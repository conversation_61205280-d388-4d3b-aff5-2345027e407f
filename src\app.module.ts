import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { AccountModule } from './account/account.module';
import { AdminModule } from './admin/admin.module';
import { AllowanceModule } from './allowance/allowance.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthTokenModule } from './auth-token/auth-token.module';
import { AuthTokenService } from './auth-token/auth-token.service';
import { AuthGuard } from './authentication/auth.guard';
import { AuthModule } from './authentication/auth.module';
import { jwtConstants } from './authentication/constant';
import { AuthorizationModule } from './authorization-queue/authorization-queue.module';
import { BranchModule } from './branch/branch.module';
import { CommonModule } from './common/common.module';
import { PrivilegeGuard } from './common/guards/privilege.guard'; // <-- Import your guard
import { AuthorizationRequestMakerModule } from './common/maker/authorization-request.maker.module';
import { CompanyModule } from './company/company.module';
import { ContractTypeModule } from './contract-type/contract-type.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { DatabaseModule } from './database/database.module';
import { DatabaseService } from './database/database.service';
import { DeductionModule } from './deduction/deduction.module';
import { DepartmentModule } from './department/department.module';
import { DesignationModule } from './designation/designation.module';
import { EmployeeGroupModule } from './employee-group/employee-group.module';
import { EmployeeModule } from './employee/employee.module';
import { EnrollmentModule } from './enrollement/enrollment.module';
import { EntityModule } from './entity/entity.module';
import { GradeModule } from './grade/grade.module';
import { LocationModule } from './location/location.module';
import { OtpModule } from './otp/otp.module';
import { PayrollModule } from './payroll/payroll.module';
import { PrivilegeModule } from './privilege/privilege.module';
import { RegionModule } from './region/region.module';
import { RoleModule } from './role/role.module';
import { RouteLoggerModule } from './route-logger/route-logger.module';
import { RouteLoggerService } from './route-logger/route-logger.service';
import { SalaryPackageModule } from './salary-package/salary-package.module';
import { SubBranchModule } from './sub-branch/sub-branch.module';
import { SwaggerModule } from './swagger/swagger.module';
import { SwaggerService } from './swagger/swagger.service';
import { TaxJurisdictionModule } from './tax-jurisdiction/tax-jurisdiction.module';
import { UnitModule } from './unit/unit.module';
import { UsersModule } from './users/users.module';
import { ValidationModule } from './validation/validation.module';

@Module({
  imports: [
    AllowanceModule,
    AuthModule,
    AdminModule,
    AccountModule,
    AuthTokenModule,
    AuthorizationModule,
    BranchModule,
    CommonModule,
    CompanyModule,
    ContractTypeModule,
    DashboardModule,
    DatabaseModule,
    DeductionModule,
    DepartmentModule,
    DesignationModule,
    EmployeeGroupModule,
    EmployeeModule,
    EnrollmentModule,
    EntityModule,
    GradeModule,
    HttpModule,
    LocationModule,
    OtpModule,
    PayrollModule,
    PrivilegeModule,
    RegionModule,
    RoleModule,
    RouteLoggerModule,
    SalaryPackageModule,
    SubBranchModule,
    TaxJurisdictionModule,
    SwaggerModule,
    UnitModule,
    UsersModule,
    ValidationModule,
    AuthorizationRequestMakerModule,
    ThrottlerModule.forRoot({
      throttlers: [
        {
          name: 'short',
          ttl: 60000, // 1 minute
          limit: 60, // 60 requests per minute (default global limit)
        },
        {
          name: 'medium',
          ttl: 3600000, // 1 hour
          limit: 1000, // 1000 requests per hour
        },
        {
          name: 'long',
          ttl: 86400000, // 24 hours
          limit: 10000, // 10000 requests per day
        },
      ],
      // Skip rate limiting for healthcheck endpoints
      skipIf: (context) => {
        const request = context.switchToHttp().getRequest<{ url?: string }>();
        return request.url === '/health' || request.url === '/';
      },
    }),
    JwtModule.register({
      global: true,
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '1h' },
    }),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    RouteLoggerService,
    SwaggerService,
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useFactory: (
        reflector: Reflector,
        authTokenService: AuthTokenService,
        databaseService: DatabaseService,
      ) => new PrivilegeGuard(reflector, authTokenService, databaseService),
      inject: [Reflector, AuthTokenService, DatabaseService],
    },
  ],
})
export class AppModule {}
