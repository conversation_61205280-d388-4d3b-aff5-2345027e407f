// src/common/crypto/crypto.service.ts
import { Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

@Injectable()
export class CryptoService {
  private readonly saltRounds = 10;
  private readonly algorithm = 'aes-256-cbc';
  private readonly key = crypto
    .createHash('sha256')
    .update(String(process.env.ENCRYPTION_KEY || 'your-secret-key'))
    .digest('base64')
    .slice(0, 32);
  private readonly iv = crypto.randomBytes(16); // Save this IV if needed for decryption

  async hash(data: string): Promise<string> {
    try {
      const hash = await bcrypt.hash(data, this.saltRounds);
      return hash;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error(
          `Something went wrong while encrypting password:\n${error.message}`,
        );
      }
      throw new Error('An unknown error occurred during hashing');
    }
  }

  async compare({
    data,
    hash,
  }: {
    data: string;
    hash: string;
  }): Promise<boolean> {
    try {
      console.log(data, hash);

      const isValid = await bcrypt.compare(data, hash);
      return isValid;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error(
          `Something went wrong while confirming password:\n${error.message}`,
        );
      }
      throw new Error('An unknown error occurred during comparison');
    }
  }

  encrypt(text: string): string {
    const cipher = crypto.createCipheriv(this.algorithm, this.key, this.iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return `${this.iv.toString('hex')}:${encrypted}`;
  }

  decrypt(encrypted: string): string {
    const [ivHex, encryptedText] = encrypted.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv(this.algorithm, this.key, iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
