import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { AllowanceService } from 'src/allowance/allowance.service';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { BranchService } from 'src/branch/branch.service';
import { CryptoModule } from 'src/common/crypto/crypto.module';
import { ContractTypeService } from 'src/contract-type/contract-type.service';
import { DatabaseModule } from 'src/database/database.module';
import { DeductionService } from 'src/deduction/deduction.service';
import { DepartmentService } from 'src/department/department.service';
import { DesignationService } from 'src/designation/designation.service';
import { EmployeeGroupService } from 'src/employee-group/employee-group.service';
import { EmployeeService } from 'src/employee/employee.service';
import { GradeService } from 'src/grade/grade.service';
import { MailService } from 'src/mail/mail.service';
import { PayrollService } from 'src/payroll/payroll.service';
import { RegionService } from 'src/region/region.service';
import { RoleService } from 'src/role/role.service';
import { SalaryPackageService } from 'src/salary-package/salary-package.service';
import { SubBranchService } from 'src/sub-branch/sub-branch.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { UnitService } from 'src/unit/unit.service';
import { UsersService } from 'src/users/users.service';
import { ValidationService } from 'src/validation/validation.service';
import { AuthorizationController } from './authorization-queue.controller';
import { AuthorizationService } from './authorization-queue.service';

@Module({
  imports: [DatabaseModule, CryptoModule, HttpModule],
  providers: [
    AuthorizationService,
    AuthTokenService,
    RoleService,
    EmployeeGroupService,
    BranchService,
    EmployeeService,
    GradeService,
    DepartmentService,
    UnitService,
    DesignationService,
    ContractTypeService,
    SalaryPackageService,
    DeductionService,
    AllowanceService,
    ValidationService,
    PayrollService,
    UsersService,
    MailService,
    TaxJurisdictionService,
    RegionService,
    SubBranchService,
  ],
  exports: [AuthorizationService],
  controllers: [AuthorizationController],
})
export class AuthorizationModule {}
