import { Body, Controller, Get, Headers, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateUnitDto } from './dto/create-unit.dto';
import { DeleteUnitDto } from './dto/delete-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';
import { UnitService } from './unit.service';

@ApiTags('Unit')
@Controller('unit')
export class UnitController {
  constructor(private readonly unitService: UnitService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new unit with the provided details.',
    dto: CreateUnitDto,
    statusCodes: [],
  })
  @RequirePrivilege('UNIT|CREATE')
  @Post('/create')
  createUnit(
    @Body() createUnitCreateUnitDto: CreateUnitDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.unitService.createUnit({
      payload: createUnitCreateUnitDto,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all units.',
    statusCodes: [],
  })
  @RequirePrivilege('UNIT|VIEW')
  @Get()
  getUnits(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.unitService.getUnits(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update grade level.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.UNIT}|${ACTIONS_CONSTANT.update}`)
  @Post('/update')
  updateUnit(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateUnitDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.unitService.updateUnitRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete unit.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.UNIT}|${ACTIONS_CONSTANT.delete}`)
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteUnitDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.unitService.deleteUnitRequest({
      payload,
      token: token,
    });
  }
}
