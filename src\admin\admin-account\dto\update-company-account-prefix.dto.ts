import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumberString, Length } from 'class-validator';

export class UpdateCompanyAccountPrefixDto {
  @ApiProperty({
    description: 'The 10-digit numeric account prefix',
    example: '**********',
  })
  @IsNotEmpty({ message: 'Account Prefix is required' })
  @IsNumberString({}, { message: 'Account Prefix must be a numeric string' })
  @Length(10, 10, { message: 'Account Prefix must be exactly 10 digits' })
  accountPrefix: string;

  @ApiProperty({ description: 'The company ID', example: 'company_123' })
  @IsNotEmpty({ message: 'Company Id is required' })
  companyId: string;
}
