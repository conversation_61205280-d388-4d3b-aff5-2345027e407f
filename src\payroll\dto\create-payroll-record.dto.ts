import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class PayrollRecordDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  staffCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  unit?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  gradeLevel?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  grossPay?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  daysWorkedPreviousPayroll?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  daysWorkedCurrentPayroll?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  apprenticeAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  specialCategoryAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  lunchSubsidyAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  monthlyBasicSalary?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  housingAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  transportAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  utilityAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  selfMaintenance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  furnitureAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  hazardAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  levelProficiency?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  fuelSubsidy?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  childEducationSubsidy?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  domesticStaff?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  responsibility?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  managementFee?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  grossPayAlt?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  amortisedGross?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  vehicleAmortisation?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  leaveAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  performanceBonus?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  inconvenienceAllowance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  overTime?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  outstandingSalary?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  iouRecovery?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  loanSalaryAdvanceDeduction?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  productCashShortage?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  lateness?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  absenteeism?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  otherPenalty?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  otherDeduction?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  cooperativeContribution?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  pension?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  paye?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  grossPayable?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  amortisedPaye?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalDeduction?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  netPay?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  unnamed46?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  unnamed47?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  percent1144?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  annualGross?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  annualPension?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  otherReliefs?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  consolidatedRelief?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  taxableIncome?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  monthlyTax?: number;

  @ApiProperty({ required: true })
  @IsOptional()
  @IsString()
  payrollId: string;
}
