import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class CreateEntityDto {
  @ApiProperty({
    example: 'STAFF',
    description:
      'The unique name of the entity/module (e.g. STAFF, LOAN, INVENTORY).',
  })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    example: 'Handles all operations related to staff management',
    description: 'A short description of what the entity is used for.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
}
