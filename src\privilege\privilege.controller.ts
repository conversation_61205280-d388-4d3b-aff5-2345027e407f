import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SwaggerService } from 'src/swagger/swagger.service';
import { PrivilegeService } from './privilege.service';

@ApiTags('Privilege')
@Controller('privilege')
export class PrivilegeController {
  constructor(private readonly privilegeService: PrivilegeService) {}

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all privileges',
    statusCodes: [],
  })
  @Get()
  getPrivileges() {
    return this.privilegeService.readPrivileges();
  }
}
