import { Body, Controller, Get, Headers, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { DeleteRegionDto } from './dto/delete-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { RegionService } from './region.service';

@ApiTags('Region')
@Controller('region')
export class RegionController {
  constructor(private readonly regionService: RegionService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new region with the provided details.',
    dto: CreateRegionDto,
    statusCodes: [],
  })
  @RequirePrivilege('REGION|CREATE')
  @Post()
  createRegion(
    @Body() createRegionCreateRegionDto: CreateRegionDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.regionService.createRegion({
      payload: createRegionCreateRegionDto,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all regions.',
    statusCodes: [],
  })
  @RequirePrivilege('REGION|VIEW')
  @Get()
  getRegions(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.regionService.getRegions(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update grade level.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.REGION}|${ACTIONS_CONSTANT.update}`)
  @Post('/update')
  updateRegion(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateRegionDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.regionService.updateRegionRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete region.',
    statusCodes: [],
  })
  @RequirePrivilege(`${MODULE_CONSTANT.REGION}|${ACTIONS_CONSTANT.delete}`)
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteRegionDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.regionService.deleteRegionRequest({
      payload,
      token: token,
    });
  }
}
