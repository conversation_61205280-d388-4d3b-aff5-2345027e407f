import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationService } from 'src/authorization-queue/authorization-queue.service';
import { DatabaseService } from 'src/database/database.service';
import { NigeriaStates } from './constants/nigeria-state.constatnt';
import { CreateLocationDto } from './dto/create-location.dto';

@Injectable()
export class LocationService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationService: AuthorizationService,
  ) {}

  async findLocation({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const location = this.databaseService.location.findFirst({
      where: {
        OR: [{ id: identifier }, { name: identifier }],
      },
    });

    return location;
  }
  // Method to find a role by ID or Name
  async getLocations(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      // const { companyId } = decodedToken;
      const branches = await this.databaseService.location.findMany({
        where: {},
        include: {
          locationLga: true,
        },
      });

      return branches;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptLocationAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { name } = JSON.parse(queue.data) as CreateLocationDto;

        // Check if the group already exists by name
        const locationExist = await this.databaseService.location.findUnique({
          where: {
            name,
          },
        });

        if (locationExist) {
          throw new BadRequestException('Location already exists');
        }

        await this.databaseService.location.create({
          data: {
            name,
          },
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createLocation({
    payload,
    token,
  }: {
    payload: CreateLocationDto;
    token: string;
  }) {
    return await this.authorizationService.createRequest({
      payload,
      token,
      action: ACTIONS_CONSTANT.create,
      module: MODULE_CONSTANT.STATE,
    });
  }

  async createBulkLocation({
    payload,
    token,
  }: {
    payload: CreateLocationDto[];
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      await this.databaseService.location.createMany({
        data: payload,
        skipDuplicates: true,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async baseCreateLocation() {
    await this.databaseService.location.deleteMany();
    await this.databaseService.locationLga.deleteMany();

    const stateNames = Object.keys(NigeriaStates) as Array<
      keyof typeof NigeriaStates
    >;

    for (const state of stateNames) {
      await this.databaseService.location.create({
        data: {
          name: state,
          locationLga: {
            createMany: {
              data: NigeriaStates[state].map((lga) => ({ name: lga })),
            },
          },
        },
      });
    }

    return;
  }
}
