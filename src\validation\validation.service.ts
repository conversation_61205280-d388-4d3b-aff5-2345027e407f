/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable } from '@nestjs/common';
import { AxiosError, AxiosResponse, isAxiosError } from 'axios';
import { lastValueFrom, Observable } from 'rxjs';
import { BvnValidationDto } from './dto/bvn-validation.dto';
import { NinValidationDto } from './dto/nin-validation.dto';
import { BvnApiResponseDef } from './inteface/BvnApiResponse';
import { NinApiResponseDef } from './inteface/NinApiResponse';
import { QoreIdAuthenticationResponseDef } from './inteface/QoreIdAuthenticationResponse';

@Injectable()
export class ValidationService {
  constructor(private readonly httpService: HttpService) {}

  private readonly qoreIdAuthenticateUrl = 'https://api.qoreid.com/token';
  private readonly qoreIdVerifyBvnUrl =
    'https://api.qoreid.com/v1/ng/identities/bvn-basic';
  private readonly qoreIdVerifyNinUrl =
    'https://api.qoreid.com/v1/ng/identities/nin';
  private readonly qoreIdAuthenticate = async () => {
    try {
      const response$ = this.httpService.post(this.qoreIdAuthenticateUrl, {
        clientId: process.env.QORE_ID_CLIENT_ID_KEY,
        secret: process.env.QORE_ID_SECRET_KEY,
      }) as Observable<AxiosResponse<QoreIdAuthenticationResponseDef>>;

      const response = await lastValueFrom(response$);

      return response.data;
    } catch (error: unknown) {
      console.log('Error authenticating with Qore ID:', error);
      throw new Error('QoreId Authentication failed');
    }
  };

  async validateBvn(payload: BvnValidationDto) {
    const { bvn, firstname, lastname } = payload;
    try {
      const { accessToken } = await this.qoreIdAuthenticate();

      const response$ = this.httpService.post(
        `${this.qoreIdVerifyBvnUrl}/${bvn}`,
        {
          firstname,
          lastname,
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      ) as Observable<AxiosResponse<BvnApiResponseDef>>;

      const response = (await lastValueFrom(response$)).data;

      if (response.summary.bvn_check.status === 'NO_MATCH') {
        throw new BadRequestException('BVN has no Match');
      }

      return response;
    } catch (error: unknown) {
      console.log(error);

      if (isAxiosError(error)) {
        const message = error.response?.data?.message;

        throw new BadRequestException(
          typeof message === 'string' ? message : 'BVN validation failed',
        );
      }

      if (error instanceof Error) {
        throw new BadRequestException(error.message);
      }

      throw new BadRequestException('BVN validation failed');
    }
  }
  async validateNin(payload: NinValidationDto) {
    const { nin, firstname, lastname } = payload;
    try {
      const { accessToken } = await this.qoreIdAuthenticate();

      const response$ = this.httpService.post(
        `${this.qoreIdVerifyNinUrl}/${nin}`,
        {
          firstname,
          lastname,
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      ) as Observable<AxiosResponse<NinApiResponseDef>>;

      const response = (await lastValueFrom(response$)).data;

      if (response.summary.nin_check.status === 'NO_MATCH') {
        throw new BadRequestException('NIN has no Match');
      }
      return response;
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        throw new Error(error.response?.data.message);
      }

      if (error instanceof Error) {
        throw new BadRequestException(error.message);
      }
      console.log(error);
      throw new Error('NIN validation failed');
    }
  }
}
