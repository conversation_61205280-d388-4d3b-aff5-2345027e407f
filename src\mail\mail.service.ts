import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer';
import { Injectable } from '@nestjs/common';

@Injectable()
export class MailService {
  constructor(private readonly mailerService: MailerService) {}

  async sendEmail(params: {
    subject: string;
    html: string;
    context: ISendMailOptions['context'];
    email: string;
  }) {
    try {
      if (!params.email) {
        throw new Error(`No recipient found `);
      }

      console.log(process.env.SMTP_FROM);

      const sendMailParams = {
        to: params.email,
        from: process.env.SMTP_FROM,
        subject: params.subject,
        html: params.html,
        context: params.context,
      };

      await this.mailerService.sendMail(sendMailParams);

      return {
        info: 'Email sent successfully',
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
