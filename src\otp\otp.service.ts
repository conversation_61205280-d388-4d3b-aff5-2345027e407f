import { BadRequestException, Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { MailService } from 'src/mail/mail.service';
import { RequestOtpDto } from './dto/request-otp.dto';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { VerifyAuthOtpDto } from './dto/verify-auth-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';

@Injectable()
export class OtpService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly mailService: MailService,
  ) {}

  private generateOtp(length = 6): string {
    return Math.floor(100000 + Math.random() * 900000)
      .toString()
      .slice(0, length);
  }

  async requestOtp(dto: RequestOtpDto) {
    const code = this.generateOtp();

    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    if (dto.companyId) {
      await this.databaseService.otp.deleteMany({
        where: {
          email: dto.email,
          companyId: dto.companyId,
        },
      });
    } else {
      await this.databaseService.otp.deleteMany({
        where: {
          email: dto.email,
          companyId: null,
        },
      });
    }

    const otp = await this.databaseService.otp.create({
      data: {
        email: dto.email,
        companyId: dto.companyId,
        code,
        expiresAt,
      },
    });

    // TODO: send OTP via email or SMS here
    await this.mailService.sendEmail({
      subject: 'Welcome to CoreHR',
      html: `<h1>OTP Verification</h1><p>Your OTP is: ${otp.code}</p>`,
      context: {
        name: 'John Doe',
      },
      email: otp.email,
    });
    return { message: 'OTP sent', otpRef: otp.id }; // Remove `code` in production!
  }

  async resendOtp(dto: ResendOtpDto) {
    const code = this.generateOtp();

    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    const otpExist = await this.databaseService.otp.findUnique({
      where: {
        id: dto.otpRef,
      },
    });

    if (!otpExist) {
      throw new BadRequestException('Invalid request');
    }
    const otp = await this.databaseService.otp.update({
      where: {
        id: otpExist.id,
      },
      data: {
        code,
        expiresAt,
      },
    });

    if (!otp) {
      throw new BadRequestException('Invalid or expired OTP');
    }

    await this.mailService.sendEmail({
      subject: 'Welcome to CoreHR',
      html: `<h1>OTP Verification</h1><p>Your OTP is: ${code}</p>`,
      context: {
        name: 'John Doe',
      },
      email: otpExist.email,
    });

    return;
  }

  async verifyOtp(dto: VerifyOtpDto) {
    const otp = await this.databaseService.otp.findFirst({
      where: {
        email: dto.email,
        code: dto.code,
        expiresAt: { gt: new Date() },
      },
    });
    console.log(otp);

    if (!otp) {
      throw new BadRequestException('Invalid or expired OTP');
    }

    await this.databaseService.otp.deleteMany({
      where: { email: dto.email },
    });

    return { otpVerified: true };
  }

  async verifyAuthOtp(dto: VerifyAuthOtpDto) {
    const otp = await this.databaseService.otp.findFirst({
      where: {
        email: dto.email,
        code: dto.code,
        companyId: dto.companyId,
        expiresAt: { gt: new Date() },
      },
    });
    console.log(otp);

    if (!otp) {
      throw new BadRequestException('Invalid or expired OTP');
    }

    await this.databaseService.otp.deleteMany({
      where: { email: dto.email },
    });

    return { otpVerified: true };
  }
}
