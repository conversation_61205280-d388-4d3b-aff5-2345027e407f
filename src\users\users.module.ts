import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { CryptoModule } from 'src/common/crypto/crypto.module';
import { DatabaseModule } from 'src/database/database.module';
import { MailService } from 'src/mail/mail.service';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';

@Module({
  imports: [DatabaseModule, CryptoModule],
  providers: [UsersService, AuthTokenService, MailService],
  controllers: [UsersController],
})
export class UsersModule {}
