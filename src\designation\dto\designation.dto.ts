import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class CreateDesignationDto {
  @ApiProperty({
    description: 'Name of the designation. Must be unique within a company.',
    maxLength: 255,
    example: 'Management Staff',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    example: 'All management staff',
    description: 'A short description of the grade',
    required: false,
  })
  @IsString()
  @MaxLength(255)
  description?: string;
}
