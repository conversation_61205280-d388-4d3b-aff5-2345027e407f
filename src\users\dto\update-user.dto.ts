import { ApiProperty } from '@nestjs/swagger';
import {
  IsA<PERSON>y,
  IsBoolean,
  IsEmail,
  IsInt,
  IsOptional,
  IsString,
} from 'class-validator';

export class UpdateUserDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'The list of branch IDs the user belongs to.',
    example: ['1', '2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  branches: string[];

  @ApiProperty()
  @IsInt()
  roleId: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  hasAccessToAllBranches?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isRoot?: boolean;
}
