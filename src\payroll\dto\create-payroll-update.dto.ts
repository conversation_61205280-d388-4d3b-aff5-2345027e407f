import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { PayrollRecordDto } from './create-payroll-record.dto';

export class CreatePayrollUploadDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  period: string; // e.g. "2024-05"

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  payDate?: string; // e.g. "2024-05-05"

  @ApiProperty({ type: [PayrollRecordDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PayrollRecordDto)
  records: PayrollRecordDto[];
}
