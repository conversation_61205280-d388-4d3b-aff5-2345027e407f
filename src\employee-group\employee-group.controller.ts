import { Body, Controller, Get, Headers, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { RequirePrivilege } from 'src/common/decorators/require-privilege.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CreateEmployeeGroupDto } from './dto/create-employee-group.dto';
import { DeleteEmployeeGroupDto } from './dto/delete-employee-group.dto';
import { UpdateEmployeeGroupDto } from './dto/update-employee-group.dto';
import { EmployeeGroupService } from './employee-group.service';

@ApiTags('Grade Level')
@Controller('employee-group')
export class EmployeeGroupController {
  constructor(private readonly employeeGroupService: EmployeeGroupService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new employeeGroup with the provided details.',
    dto: CreateEmployeeGroupDto,
    statusCodes: [],
  })
  @Post()
  createEmployeeGroup(
    @Body() createEmployeeGroupDto: CreateEmployeeGroupDto,
    @Req() request: Request,
  ) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.employeeGroupService.createEmployeeGroup({
      payload: createEmployeeGroupDto,
      token: token!,
    });
  }

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all employeeGroups.',
    statusCodes: [],
  })
  @Get()
  getEmployeeGroups(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.employeeGroupService.getEmployeeGroups(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Update grade level.',
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.EMPLOYEE_GROUP}|${ACTIONS_CONSTANT.update}`,
  )
  @Post('/update')
  updateDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: UpdateEmployeeGroupDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.employeeGroupService.updateGradeLevelRequest({
      payload,
      token: token,
    });
  }

  @SwaggerService.applyOperation({
    method: 'delete',
    description: 'Delete grade level.',
    statusCodes: [],
  })
  @RequirePrivilege(
    `${MODULE_CONSTANT.EMPLOYEE_GROUP}|${ACTIONS_CONSTANT.delete}`,
  )
  @Post('/delete')
  deleteDesignation(
    @Headers('authorization') authHeader: string,
    @Body() payload: DeleteEmployeeGroupDto,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.employeeGroupService.deleteGradeLevelRequest({
      payload,
      token: token,
    });
  }
}
