import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { PayrollRecordDto } from './payroll-record.dto';

export class CreatePayrollUploadDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  uploadedBy: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  fileUrl: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  period: string; // e.g. "2024-05"

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  payDate?: string; // e.g. "2024-05-05"

  @ApiProperty({ type: [PayrollRecordDto] })
  @IsArray()
  @ValidateNested({ each: true })
  records: PayrollRecordDto[];
}
