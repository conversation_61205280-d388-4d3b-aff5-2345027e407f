import { Injectable } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { GetPayrollByIdDto } from './dto/get-payroll-by-id.dto';

@Injectable()
export class AdminPayrollService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  async getPayrollById(payload: GetPayrollByIdDto) {
    // Todo: Confirm user is an admin
    return await this.databaseService.payrollUpload.findUnique({
      where: {
        id: payload.payrollId,
      },
    });
  }

  async getAllPayroll() {
    // Todo: Confirm user is an admin
    const payrolls = await this.databaseService.payrollUpload.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });

    console.log(payrolls);

    return payrolls;
  }
}
