import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { GetCompanyByIdDto } from './dto/get-company-by-id.dto';
import { UpdateCompanyAccountClassDto } from './dto/update-company-account-class.dto';
import { UpdateCompanyAccountPrefixDto } from './dto/update-company-account-prefix.dto';

@Injectable()
export class AdminCompanyService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
  ) {}

  generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  async getCompanyById(payload: GetCompanyByIdDto) {
    // Todo: Confirm user is an admin
    return await this.databaseService.company.findUnique({
      where: {
        id: payload.companyId,
      },
    });
  }

  async getAllCompany() {
    // Todo: Confirm user is an admin
    return await this.databaseService.company.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async updateCompanyAccountClass(payload: UpdateCompanyAccountClassDto) {
    try {
      const companyExist = await this.getCompanyById({
        companyId: payload.companyId,
      });

      if (!companyExist) {
        throw new BadRequestException('Company not found');
      }

      await this.databaseService.company.update({
        where: {
          id: companyExist.id,
        },
        data: {
          accountClass: payload.accountClass,
        },
      });
      return;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async updateCompanyAccountPrefix(payload: UpdateCompanyAccountPrefixDto) {
    try {
      const companyExist = await this.getCompanyById({
        companyId: payload.companyId,
      });

      if (!companyExist) {
        throw new BadRequestException('Company not found');
      }

      await this.databaseService.company.update({
        where: {
          id: companyExist.id,
        },
        data: {
          accountPrefix: payload.accountPrefix,
        },
      });
      return;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
