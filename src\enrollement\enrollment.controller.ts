import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  PublicRateLimit,
  OTPRateLimit,
} from 'src/common/decorators/rate-limit.decorator';
import { Public } from 'src/common/decorators/public.decorator';
import { ResendOtpDto } from 'src/otp/dto/resend-otp.dto';
import { OtpService } from 'src/otp/otp.service';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CompleteEnrollmentDto } from './dto/complete-enrollment.dto';
import { InitiateEnrollmentDto } from './dto/initiate-enrollment.dto';
import { EnrollmentService } from './enrollment.service';

@ApiTags('Enrollment')
@Controller('enrollment')
export class EnrollmentController {
  constructor(
    private readonly enrollmentService: EnrollmentService,
    private readonly otpService: OtpService,
  ) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new company with the provided details.',
    dto: InitiateEnrollmentDto,
    statusCodes: [],
  })
  @PublicRateLimit()
  @Public()
  @Post('initiate')
  initiateEnrollment(@Body() InitiateEnrollmentDto: InitiateEnrollmentDto) {
    return this.enrollmentService.initiateEnrollment(InitiateEnrollmentDto);
  }
  @PublicRateLimit()
  @Public()
  @Post('complete')
  completeEnrollment(@Body() CompleteEnrollmentDto: CompleteEnrollmentDto) {
    return this.enrollmentService.completeEnrollment(CompleteEnrollmentDto);
  }

  @OTPRateLimit()
  @Public()
  @Post('resend-otp')
  resendOtp(@Body() payload: ResendOtpDto) {
    return this.otpService.resendOtp(payload);
  }
}
