import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { PayrollCronModule } from './cron/payroll-cron.module';
import { PayrollController } from './payroll.controller';
import { PayrollService } from './payroll.service';

@Module({
  imports: [DatabaseModule, PayrollCronModule],
  providers: [PayrollService, AuthTokenService],
  controllers: [PayrollController],
})
export class PayrollModule {}
