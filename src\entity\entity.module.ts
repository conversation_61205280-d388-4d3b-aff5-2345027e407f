import { Module } from '@nestjs/common';
import { CryptoModule } from 'src/common/crypto/crypto.module';
import { DatabaseModule } from 'src/database/database.module';
import { EntityController } from './entity.controller';
import { EntityService } from './entity.service';

@Module({
  imports: [DatabaseModule, CryptoModule],
  providers: [EntityService],
  controllers: [EntityController],
})
export class EntityModule {}
