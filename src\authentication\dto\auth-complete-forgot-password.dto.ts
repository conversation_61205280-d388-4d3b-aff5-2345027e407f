import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsInt, IsOptional, IsString } from 'class-validator';

export class AuthCompleteForgotPasswordDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  accountId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiProperty()
  @IsEmail({}, { message: 'Kindly provide a valid email' })
  @Transform(({ value }: { value: string }) => value?.toLowerCase())
  email: string;

  @ApiProperty()
  password: string;

  @ApiProperty()
  otp: string;
}
