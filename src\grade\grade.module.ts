import { Module } from '@nestjs/common';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseModule } from 'src/database/database.module';
import { GradeController } from './grade.controller';
import { GradeService } from './grade.service';

@Module({
  imports: [DatabaseModule],
  providers: [GradeService, AuthTokenService],
  controllers: [GradeController],
})
export class GradeModule {}
