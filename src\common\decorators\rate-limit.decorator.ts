import { Throttle } from '@nestjs/throttler';

/**
 * Rate limiting decorators based on industry best practices
 * These decorators apply different rate limits based on endpoint types
 */

/**
 * For authentication endpoints (login, register, password reset)
 * Stricter limits to prevent brute force attacks
 * 5 requests per minute, 20 per hour
 */
export const AuthRateLimit = () =>
  Throttle({
    short: { ttl: 60000, limit: 5 }, // 5 requests per minute
    medium: { ttl: 3600000, limit: 20 }, // 20 requests per hour
  });

/**
 * For public endpoints (account creation, company lookup)
 * Moderate limits to prevent abuse while allowing legitimate use
 * 10 requests per minute, 100 per hour
 */
export const PublicRateLimit = () =>
  Throttle({
    short: { ttl: 60000, limit: 10 }, // 10 requests per minute
    medium: { ttl: 3600000, limit: 100 }, // 100 requests per hour
  });

/**
 * For sensitive operations (payroll, financial data)
 * Conservative limits for critical business operations
 * 30 requests per minute, 200 per hour
 */
export const SensitiveRateLimit = () =>
  Throttle({
    short: { ttl: 60000, limit: 30 }, // 30 requests per minute
    medium: { ttl: 3600000, limit: 200 }, // 200 requests per hour
  });

/**
 * For bulk operations (file uploads, bulk creates)
 * Very strict limits due to resource intensity
 * 5 requests per 5 minutes, 20 per hour
 */
export const BulkOperationRateLimit = () =>
  Throttle({
    short: { ttl: 300000, limit: 5 }, // 5 requests per 5 minutes
    medium: { ttl: 3600000, limit: 20 }, // 20 requests per hour
  });

/**
 * For read operations (GET requests for lists, data retrieval)
 * More permissive limits for data consumption
 * 100 requests per minute, 1000 per hour
 */
export const ReadRateLimit = () =>
  Throttle({
    short: { ttl: 60000, limit: 100 }, // 100 requests per minute
    medium: { ttl: 3600000, limit: 1000 }, // 1000 requests per hour
  });

/**
 * For write operations (POST, PUT, PATCH, DELETE)
 * Standard limits for data modification
 * 50 requests per minute, 500 per hour
 */
export const WriteRateLimit = () =>
  Throttle({
    short: { ttl: 60000, limit: 50 }, // 50 requests per minute
    medium: { ttl: 3600000, limit: 500 }, // 500 requests per hour
  });

/**
 * For admin operations
 * Moderate limits for administrative tasks
 * 60 requests per minute, 600 per hour
 */
export const AdminRateLimit = () =>
  Throttle({
    short: { ttl: 60000, limit: 60 }, // 60 requests per minute
    medium: { ttl: 3600000, limit: 600 }, // 600 requests per hour
  });

/**
 * For OTP operations (generation, verification)
 * Very strict limits to prevent abuse
 * 3 requests per minute, 10 per hour
 */
export const OTPRateLimit = () =>
  Throttle({
    short: { ttl: 60000, limit: 3 }, // 3 requests per minute
    medium: { ttl: 3600000, limit: 10 }, // 10 requests per hour
  });
