import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { DatabaseService } from 'src/database/database.service';
import { ValidationService } from 'src/validation/validation.service';
import { PayrollService } from '../payroll.service';
import { PayrollCronController } from './payroll-cron.controller';
import { PayrollCronService } from './payroll-cron.service';

@Module({
  imports: [ScheduleModule.forRoot(), HttpModule],
  providers: [
    PayrollCronService,
    DatabaseService,
    PayrollService,
    AuthTokenService,
    ValidationService,
  ],
  controllers: [PayrollCronController],
})
export class PayrollCronModule {}
