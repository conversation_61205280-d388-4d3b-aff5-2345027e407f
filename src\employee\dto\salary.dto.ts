import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsOptional, IsString } from 'class-validator';

export class SalaryDto {
  @ApiProperty({ example: '300000' })
  @Allow()
  @IsString()
  grossSalary: string;

  @ApiProperty({ example: '50000' })
  @Allow()
  @IsString()
  tax: string;

  @ApiProperty({ example: '20000', required: false })
  @Allow()
  @IsOptional()
  @IsString()
  otherDeductions?: string;

  @ApiProperty({ example: 'NGN', required: false })
  @Allow()
  @IsString()
  currency: string;
}
