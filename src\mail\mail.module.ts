import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';
import { MailService } from './mail.service';

@Module({
  imports: [
    MailerModule.forRootAsync({
      useFactory: () => ({
        transport: {
          host: process.env.SMTP_HOST,
          port: 465,
          secure: true,
          auth: {
            pass: process.env.SMTP_FROM_PASSWORD,
            user: process.env.SMTP_USER,
          },
        },
        defaults: {
          from: process.env.SMTP_FROM,
        },
        // template: {
        //   dir: __dirname + '/../../templates',
        //   adapter: new PugAdapter(),
        //   options: {
        //     strict: true,
        //   },
        // },
      }),
    }),
  ],
  providers: [MailService],
  exports: [MailService],
  controllers: [],
})
export class MailModule {}
