import { ConsoleLogger, Injectable, NestMiddleware } from "@nestjs/common";

@Injectable()
export class RouteLoggerService
  extends ConsoleLogger
  implements NestMiddleware
{
  use(req: any, res: any, next: (error?: Error | any) => void) {
    const start = Date.now();
    this.debug(`Incoming request: [${req.method}] ${req.originalUrl}`); // Debug incoming request
    res.on("finish", () => {
      const duration = Date.now() - start;
      this.log(
        `[${req.method}] ${req.originalUrl} - ${res.statusCode} (${duration}ms)`,
      );
    });
    next();
  }
}
