import { Body, Controller, Headers, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PublicRateLimit } from 'src/common/decorators/rate-limit.decorator';
import { Public } from 'src/common/decorators/public.decorator';
import { SwaggerService } from 'src/swagger/swagger.service';
import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';

@ApiTags('Company')
@Controller('company')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}
  @SwaggerService.applyOperation({
    method: 'create',
    description: 'Creates a new company with the provided details.',
    dto: CreateCompanyDto,
    statusCodes: [],
  })
  @PublicRateLimit()
  @Post('create')
  @Public()
  createCompany(
    @Body() payload: CreateCompanyDto,
    @Headers('authorization') authHeader: string,
  ) {
    const token = authHeader?.split(' ')[1];

    return this.companyService.createCompany({ payload, token });
  }
}
