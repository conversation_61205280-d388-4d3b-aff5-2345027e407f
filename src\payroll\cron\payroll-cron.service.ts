import { BadRequestException, Injectable, Logger } from '@nestjs/common';

import { Cron, CronExpression } from '@nestjs/schedule';
import { Status } from '@prisma/client';
import { DatabaseService } from 'src/database/database.service';
import { PayrollRecordDto } from '../dto/create-payroll-record.dto';
import { PayrollService } from '../payroll.service';

@Injectable()
export class PayrollCronService {
  private readonly logger = new Logger(PayrollService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly payrollService: PayrollService,
  ) {}

  async handlePendingBulkJobs() {
    const records = await this.db.payrollUploadTemp.findMany({
      where: { status: Status.PENDING },
      take: 20,
    });

    this.logger.log(`Processing ${records.length} pending payroll records`);

    for (const record of records) {
      try {
        await this.payrollService.createPayrollRecord({
          payload: {
            ...(JSON.parse(record.payload) as PayrollRecordDto),
            payrollId: record.payrollUploadId,
          },
        });

        // NOTE: If you plan to delete take care of the update as well
        // if (payrollRecord) {
        //   await this.db.payrollUploadTemp.delete({
        //     where: { id: record.id },
        //   });
        // }
      } catch (error) {
        this.logger.error(`Failed to process record ${record.id}: ${error}`);

        let reason = 'Server error';

        if (error instanceof BadRequestException) {
          const res = error.getResponse();

          if (typeof res === 'string') {
            reason = res;
          } else if (
            typeof res === 'object' &&
            res !== null &&
            'message' in res
          ) {
            const message = (res as { message: string | string[] }).message;
            reason = Array.isArray(message) ? message.join(', ') : message;
          }
        }

        await this.db.payrollUploadTemp.update({
          where: { id: record.id },
          data: {
            status: Status.FAILED,
            failureReason: reason,
          },
        });
      }
    }
  }
  async finalizeCompletedJobs() {
    // 1. Fetch up to 10 payroll upload jobs where:
    // - the job itself is still pending
    // - AND there are no associated temp records still pending
    const pendingJobs = await this.db.payrollUpload.findMany({
      where: {
        status: Status.PENDING,
        payrollUploadTemp: {
          none: {
            status: Status.PENDING,
          },
        },
      },
      take: 10,
    });

    // 2. Finalize each eligible job
    for (const job of pendingJobs) {
      await this.db.payrollUpload.update({
        where: { id: job.id },
        data: {
          status: Status.COMPLETED,
        },
      });
      this.logger.log(`✅ Finalized payroll upload job: ${job.id}`);
    }

    if (pendingJobs.length === 0) {
      this.logger.log(
        `ℹ️ No payroll uploads ready for finalization at this time.`,
      );
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleCron() {
    await this.handlePendingBulkJobs();
    await this.finalizeCompletedJobs();
  }
}
